# 免费简历模板API接口文档

## 概述

本文档描述了微信小程序免费简历模板功能的后端API接口。这些接口用于展示免费简历资源的缩略图，以及获取对应的下载链接。

**基础URL**: `http://localhost:18080`

## 接口列表

### 1. 获取所有模板列表

**接口地址**: `GET /free-templates/`

**功能描述**: 获取所有免费简历模板列表，用于微信端展示缩略图

**请求参数**:
- `skip` (可选): 跳过的记录数，默认为0
- `limit` (可选): 返回的记录数，默认为100，最大1000
- `batch_flag` (可选): 批次标识筛选
- `type` (可选): 文件类型筛选

**请求示例**:
```
GET /free-templates/?skip=0&limit=10&batch_flag=blackWhite
```

**响应示例**:
```json
{
  "total": 3,
  "templates": [
    {
      "id": "blackWhite/10.jpg",
      "thumb_url": "http://localhost:18080/static/free_resume_templates/blackWhite/10.jpg",
      "baidu_url": "https://pan.baidu.com/s/1234567890",
      "baidu_pass": "00aa",
      "quark_url": "https://sxsdlfkjslfjsdf",
      "quark_pass": "00aa",
      "type": "word"
    }
  ]
}
```

### 2. 获取批次列表

**接口地址**: `GET /free-templates/batches`

**功能描述**: 获取所有可用的批次标识列表

**请求参数**: 无

**响应示例**:
```json
["blackWhite", "colorful"]
```

### 3. 根据批次获取模板

**接口地址**: `GET /free-templates/batch/{batch_flag}`

**功能描述**: 获取指定批次的所有模板

**路径参数**:
- `batch_flag`: 批次标识

**请求参数**:
- `skip` (可选): 跳过的记录数，默认为0
- `limit` (可选): 返回的记录数，默认为100

**请求示例**:
```
GET /free-templates/batch/blackWhite?skip=0&limit=10
```

**响应示例**:
```json
{
  "batch_flag": "blackWhite",
  "total": 2,
  "templates": [
    {
      "id": "blackWhite/10.jpg",
      "thumb_url": "http://localhost:18080/static/free_resume_templates/blackWhite/10.jpg",
      "baidu_url": "https://pan.baidu.com/s/1234567890",
      "baidu_pass": "00aa",
      "quark_url": "https://sxsdlfkjslfjsdf",
      "quark_pass": "00aa",
      "type": "word"
    }
  ]
}
```

### 4. 获取下载链接 ⭐

**接口地址**: `GET /free-templates/{template_id}/download`

**功能描述**: 获取指定模板的下载链接，微信端用户点击缩略图后调用此接口

**路径参数**:
- `template_id`: 模板ID（需要URL编码，如 `blackWhite/10.jpg` 编码为 `blackWhite%2F10.jpg`）

**请求示例**:
```
GET /free-templates/blackWhite%2F10.jpg/download
```

**响应示例**:
```json
{
  "id": "blackWhite/10.jpg",
  "baidu_url": "https://pan.baidu.com/s/1234567890",
  "baidu_pass": "00aa",
  "quark_url": "https://sxsdlfkjslfjsdf",
  "quark_pass": "00aa",
  "download_count": 2,
  "message": "获取下载链接成功"
}
```

**注意**: 每次调用此接口会自动增加该模板的下载次数

### 5. 获取模板详情

**接口地址**: `GET /free-templates/{template_id}`

**功能描述**: 获取指定模板的详细信息

**路径参数**:
- `template_id`: 模板ID（需要URL编码）

**请求示例**:
```
GET /free-templates/colorful%2F01.jpg
```

**响应示例**:
```json
{
  "id": "blackWhite/1.jpg",
  "thumb_url": "http://localhost:18080/static/free_resume_templates/blackWhite/1.jpg",
  "baidu_url": "https://pan.baidu.com/s/1234567892",
  "baidu_pass": "00cc",
  "quark_url": "https://sxsdlfkjslfjsdf2",
  "quark_pass": "00cc",
  "type": "word"
}
```

### 6. 获取简历样式模板列表 ⭐

**接口地址**: `GET /free-templates/styles`

**功能描述**: 获取可用的简历样式模板列表，用于在简历样式页面展示给用户选择

**数据来源**: 从 `resume_thumbs` 表中查询 `batch_flag` 为 "template" 的记录

**请求参数**:
- `skip` (可选): 跳过的记录数，用于分页，默认为0
- `limit` (可选): 每页返回的记录数，默认为20，最大50
- `sort` (可选): 排序方式，默认为`popular`，可选值：
  - `popular`: 按热门程度排序（sort_index升序，数值越小越靠前）
  - `newest`: 按最新时间排序（创建时间降序）
  - `rating`: 按评分排序（暂时按sort_index升序）

**请求示例**:
```
GET /free-templates/styles?skip=0&limit=20&sort=popular
```

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "templates": [
      {
        "id": "template_001",
        "thumb_url": "http://localhost:18082/static/resume_templates/template_001.jpg"
      },
      {
        "id": "template_002",
        "thumb_url": "http://localhost:18082/static/resume_templates/template_002.jpg"
      }
    ],
    "total": 5,
    "skip": 0,
    "limit": 20,
    "has_more": false
  }
}
```

**响应字段说明**:
- `code`: 响应状态码，200表示成功
- `message`: 响应消息
- `data.templates`: 模板列表，只包含id和thumb_url两个字段
- `data.total`: 符合条件的模板总数
- `data.skip`: 当前跳过的记录数
- `data.limit`: 当前页面大小
- `data.has_more`: 是否还有更多数据

**数据表结构说明**:
- 数据来源：`resume_thumbs` 表
- 筛选条件：`batch_flag = 'template'`
- 排序字段：`sort_index`（数值越小越靠前）

### 7. 获取统计信息

**接口地址**: `GET /free-templates/stats/summary`

**功能描述**: 获取模板的统计信息

**请求参数**: 无

**响应示例**:
```json
{
  "total_templates": 3,
  "total_downloads": 19,
  "batch_stats": [
    {
      "batch_flag": "blackWhite",
      "template_count": 2,
      "download_count": 7
    },
    {
      "batch_flag": "colorful",
      "template_count": 1,
      "download_count": 12
    }
  ],
  "type_stats": [
    {
      "type": "word",
      "count": 3
    }
  ]
}
```

## 数据模型

### 微信端模板信息模型

**注意**: 为了减少数据传输量和提高性能，微信端接口只返回必要的字段。

| 字段名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| id | string | 模板ID（主键） | "blackWhite/10.jpg" |
| thumb_url | string | 缩略图完整URL | "http://localhost:18080/static/free_resume_templates/blackWhite/10.jpg" |
| baidu_url | string | 百度网盘链接 | "https://pan.baidu.com/s/1234567890" |
| baidu_pass | string | 百度网盘提取码 | "00aa" |
| quark_url | string | 夸克网盘链接 | "https://sxsdlfkjslfjsdf" |
| quark_pass | string | 夸克网盘提取码 | "00aa" |
| type | string | 文件类型 | "word" |

### 数据库完整模型

服务端数据库存储的完整字段（仅供参考）：

| 字段名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| id | string | 模板ID（主键） | "blackWhite/10.jpg" |
| batch_flag | string | 批次标识 | "blackWhite" |
| thumb_path | string | 缩略图相对路径 | "free_resume_templates/blackWhite/10.jpg" |
| baidu_url | string | 百度网盘链接 | "https://pan.baidu.com/s/1234567890" |
| baidu_pass | string | 百度网盘提取码 | "00aa" |
| quark_url | string | 夸克网盘链接 | "https://sxsdlfkjslfjsdf" |
| quark_pass | string | 夸克网盘提取码 | "00aa" |
| download_count | integer | 下载次数 | 0 |
| type | string | 文件类型 | "word" |
| created_at | datetime | 创建时间 | "2025-06-12T21:14:16" |
| updated_at | datetime | 更新时间 | "2025-06-12T21:14:16" |

## 微信小程序对接建议

### 1. 展示页面流程

#### 普通模板列表页面
1. **加载模板列表**: 调用 `GET /free-templates/` 获取所有模板
2. **展示缩略图**: 直接使用 `thumb_url` 字段作为图片URL，无需额外处理
3. **分类展示**: 可以使用 `GET /free-templates/batches` 获取分类，然后按批次展示

#### 简历样式选择页面 ⭐
1. **加载样式模板**: 调用 `GET /free-templates/styles` 获取样式模板列表
2. **分类筛选**: 使用 `category` 参数进行分类筛选（business/creative/simple）
3. **排序选择**: 使用 `sort` 参数选择排序方式（popular/newest/rating）
4. **分页加载**: 使用 `skip` 和 `limit` 参数实现分页或无限滚动
5. **精简展示**: 只显示缩略图，减少数据传输量，提升加载速度

### 2. 下载流程

1. **用户点击缩略图**: 调用 `GET /free-templates/{template_id}/download`
2. **展示下载链接**: 向用户展示百度网盘和夸克网盘的链接及提取码
3. **复制功能**: 提供一键复制链接和提取码的功能

### 3. URL编码注意事项

由于模板ID包含斜杠（如 `blackWhite/10.jpg`），在作为URL路径参数时需要进行URL编码：

```javascript
// JavaScript示例
const templateId = "blackWhite/10.jpg";
const encodedId = encodeURIComponent(templateId);
const url = `/free-templates/${encodedId}/download`;
```

### 4. 错误处理

所有接口都会返回标准的HTTP状态码：
- `200`: 成功
- `404`: 资源不存在
- `500`: 服务器内部错误

错误响应格式：
```json
{
  "detail": "错误描述信息"
}
```

## 管理接口（可选）

### 创建模板

**接口地址**: `POST /free-templates/`

**功能描述**: 创建新的免费模板（后台管理用）

### 更新模板

**接口地址**: `PUT /free-templates/{template_id}`

**功能描述**: 更新模板信息（后台管理用）

## 重要说明

### 缩略图URL处理

1. **自动生成**: API会自动根据请求的域名和端口生成完整的缩略图URL
2. **字段说明**:
   - `thumb_path`: 存储在数据库中的相对路径
   - `thumb_url`: API动态生成的完整URL，微信端直接使用此字段
3. **URL格式**: `{protocol}://{host}/static/{thumb_path}`
4. **环境适配**: 在不同环境（开发、测试、生产）中会自动适配对应的域名

### 静态资源部署

确保缩略图文件已正确部署到 `static/` 目录下，目录结构如下：
```
static/
└── free_resume_templates/
    ├── blackWhite/
    │   ├── 1.jpg
    │   ├── 2.jpg
    │   └── ...
    └── coverResume/
        ├── 1.png
        ├── 2.png
        └── ...
```

---

**注意**: 本文档中的示例数据仅供参考，实际使用时请根据真实数据进行调整。
