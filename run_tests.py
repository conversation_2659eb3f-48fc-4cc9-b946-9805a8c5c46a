#!/usr/bin/env python3
"""
测试运行脚本
"""
import os
import sys
import subprocess
import argparse
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def run_unit_tests():
    """运行单元测试"""
    print("🧪 运行单元测试...")
    
    test_files = [
        "tests/test_payment_service.py",
        "tests/test_payment_api.py"
    ]
    
    success_count = 0
    total_count = len(test_files)
    
    for test_file in test_files:
        if Path(test_file).exists():
            print(f"\n📝 运行测试文件: {test_file}")
            try:
                result = subprocess.run([
                    sys.executable, "-m", "unittest", test_file.replace("/", ".").replace(".py", "")
                ], capture_output=True, text=True, cwd=".")
                
                if result.returncode == 0:
                    print(f"✅ {test_file} 测试通过")
                    success_count += 1
                else:
                    print(f"❌ {test_file} 测试失败")
                    print("错误输出:")
                    print(result.stderr)
                    
            except Exception as e:
                print(f"❌ 运行 {test_file} 异常: {e}")
        else:
            print(f"⚠️ 测试文件不存在: {test_file}")
    
    print(f"\n📊 单元测试结果: {success_count}/{total_count} 通过")
    return success_count == total_count


def run_api_tests(base_url="http://localhost:18080", mode="basic"):
    """运行API测试"""
    print(f"🌐 运行API测试 (模式: {mode})...")
    
    try:
        # 检查服务是否运行
        import requests
        response = requests.get(f"{base_url}/health/ping", timeout=5)
        if response.status_code != 200:
            print(f"❌ 服务未运行或不可访问: {base_url}")
            return False
        
        print(f"✅ 服务运行正常: {base_url}")
        
        # 运行API测试
        result = subprocess.run([
            sys.executable, "test_payment_api.py", 
            "--mode", mode,
            "--base-url", base_url
        ], cwd=".")
        
        return result.returncode == 0
        
    except requests.RequestException:
        print(f"❌ 无法连接到服务: {base_url}")
        print("请确保服务正在运行")
        return False
    except Exception as e:
        print(f"❌ API测试异常: {e}")
        return False


def run_database_tests():
    """运行数据库测试"""
    print("🗄️ 运行数据库测试...")
    
    try:
        # 测试数据库连接
        from app.database import engine
        with engine.connect() as conn:
            result = conn.execute("SELECT 1")
            result.fetchone()
        
        print("✅ 数据库连接测试通过")
        
        # 测试支付相关表是否存在
        tables_to_check = [
            "membership_plans",
            "orders", 
            "payment_records",
            "user_memberships",
            "payment_logs"
        ]
        
        with engine.connect() as conn:
            for table in tables_to_check:
                result = conn.execute(f"SHOW TABLES LIKE '{table}'")
                if result.fetchone():
                    print(f"✅ 表 {table} 存在")
                else:
                    print(f"❌ 表 {table} 不存在")
                    return False
        
        print("✅ 数据库表结构测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False


def run_config_tests():
    """运行配置测试"""
    print("⚙️ 运行配置测试...")
    
    try:
        from config.wechat_pay_config import get_wechat_pay_config, validate_wechat_pay_config
        
        # 获取配置
        config = get_wechat_pay_config()
        print("✅ 微信支付配置加载成功")
        
        # 检查关键配置项
        key_configs = ["appid", "mchid", "notify_url"]
        for key in key_configs:
            if config.get(key):
                print(f"✅ 配置项 {key} 已设置")
            else:
                print(f"⚠️ 配置项 {key} 未设置")
        
        # 验证配置
        try:
            validate_wechat_pay_config()
            print("✅ 微信支付配置验证通过")
        except ValueError as e:
            print(f"⚠️ 微信支付配置验证失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False


def run_security_tests():
    """运行安全测试"""
    print("🔒 运行安全测试...")
    
    try:
        from app.services.payment_security import payment_security_service
        
        # 测试安全服务初始化
        print("✅ 支付安全服务初始化成功")
        
        # 测试配置项
        security_configs = [
            "max_order_per_user_per_hour",
            "max_payment_attempts_per_order", 
            "suspicious_ip_threshold",
            "order_timeout_minutes"
        ]
        
        for config in security_configs:
            if hasattr(payment_security_service, config):
                value = getattr(payment_security_service, config)
                print(f"✅ 安全配置 {config}: {value}")
            else:
                print(f"❌ 安全配置 {config} 缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ 安全测试失败: {e}")
        return False


def generate_test_report(results):
    """生成测试报告"""
    print("\n" + "="*60)
    print("📋 测试报告")
    print("="*60)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
    
    print("-"*60)
    print(f"总计: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查上述输出")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="支付功能测试运行脚本")
    parser.add_argument("--unit", action="store_true", help="运行单元测试")
    parser.add_argument("--api", action="store_true", help="运行API测试")
    parser.add_argument("--database", action="store_true", help="运行数据库测试")
    parser.add_argument("--config", action="store_true", help="运行配置测试")
    parser.add_argument("--security", action="store_true", help="运行安全测试")
    parser.add_argument("--all", action="store_true", help="运行所有测试")
    parser.add_argument("--base-url", default="http://localhost:18080", help="API服务地址")
    parser.add_argument("--api-mode", choices=["basic", "comprehensive"], default="basic", 
                       help="API测试模式")
    
    args = parser.parse_args()
    
    # 如果没有指定任何测试，默认运行所有测试
    if not any([args.unit, args.api, args.database, args.config, args.security]):
        args.all = True
    
    results = {}
    
    if args.all or args.config:
        results["配置测试"] = run_config_tests()
    
    if args.all or args.database:
        results["数据库测试"] = run_database_tests()
    
    if args.all or args.security:
        results["安全测试"] = run_security_tests()
    
    if args.all or args.unit:
        results["单元测试"] = run_unit_tests()
    
    if args.all or args.api:
        results["API测试"] = run_api_tests(args.base_url, args.api_mode)
    
    # 生成测试报告
    success = generate_test_report(results)
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
