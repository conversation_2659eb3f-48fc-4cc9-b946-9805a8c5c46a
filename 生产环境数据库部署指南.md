# 生产环境数据库部署指南

## 概述

本指南详细说明了微信小程序后端服务数据库在生产环境的完整部署步骤，包括MySQL数据库的安装、配置、初始化、迁移和维护。

## 1. 环境准备

### 1.1 系统要求
- 操作系统：Ubuntu 20.04+ / CentOS 7+ / RHEL 8+
- 内存：至少 4GB RAM（推荐 8GB+）
- 存储：至少 50GB 可用空间（推荐 SSD）
- 网络：稳定的网络连接

### 1.2 安装MySQL 8.0

#### Ubuntu/Debian系统：
```bash
# 更新包管理器
sudo apt update

# 安装MySQL服务器
sudo apt install mysql-server-8.0

# 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql
```

#### CentOS/RHEL系统：
```bash
# 添加MySQL官方仓库
sudo dnf install mysql80-community-release-el8-1.noarch.rpm

# 安装MySQL服务器
sudo dnf install mysql-community-server

# 启动MySQL服务
sudo systemctl start mysqld
sudo systemctl enable mysqld
```

## 2. MySQL安全配置

### 2.1 运行安全配置脚本
```bash
sudo mysql_secure_installation
```

配置选项：
- 设置root密码：选择强密码
- 移除匿名用户：Yes
- 禁止root远程登录：Yes（推荐）
- 移除测试数据库：Yes
- 重新加载权限表：Yes

### 2.2 MySQL配置优化

编辑MySQL配置文件：
```bash
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf
```

添加/修改以下配置：
```ini
[mysqld]
# 基础配置
bind-address = 127.0.0.1
port = 3306
max_connections = 200
max_allowed_packet = 64M

# 字符集配置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# InnoDB配置
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 1
innodb_file_per_table = 1

# 日志配置
log-error = /var/log/mysql/error.log
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 二进制日志（用于备份和复制）
log-bin = mysql-bin
binlog_format = ROW
expire_logs_days = 7
```

重启MySQL服务：
```bash
sudo systemctl restart mysql
```

## 3. 数据库和用户创建

### 3.1 登录MySQL
```bash
mysql -u root -p
```

### 3.2 创建数据库
```sql
-- 创建数据库
CREATE DATABASE resume_service 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 验证数据库创建
SHOW DATABASES;
```

### 3.3 创建专用用户
```sql
-- 创建应用用户
CREATE USER 'resume_user'@'localhost' IDENTIFIED BY 'Resume123!@#Prod';

-- 授予权限
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, ALTER, INDEX, DROP 
ON resume_service.* TO 'resume_user'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;

-- 验证用户创建
SELECT User, Host FROM mysql.user WHERE User = 'resume_user';
```

### 3.4 测试连接
```bash
mysql -u resume_user -p resume_service
```

## 4. 数据库表结构迁移

### 4.1 更新迁移脚本配置

修改 `migrate_error_report.py` 中的数据库配置：
```python
DB_CONFIG = {
    'host': 'localhost',
    'database': 'resume_service',
    'user': 'resume_user',
    'password': 'Resume123!@#Prod'  # 使用生产环境密码
}
```

### 4.2 执行基础表创建
```bash
# 确保脚本有执行权限
chmod +x migrate_error_report.py

# 执行迁移
python3 migrate_error_report.py
```

### 4.3 执行字段更新迁移（如果需要）
```bash
# 如果是从旧版本升级，执行字段更新
chmod +x migrate_error_report_update.py
python3 migrate_error_report_update.py
```

## 5. 应用配置更新

### 5.1 更新FastAPI数据库配置

修改 `app/database.py` 或相关配置文件：
```python
# 生产环境数据库URL
DATABASE_URL = "mysql+pymysql://resume_user:Resume123!@#Prod@localhost/resume_service"

# 或使用环境变量
import os
DATABASE_URL = os.getenv(
    "DATABASE_URL", 
    "mysql+pymysql://resume_user:Resume123!@#Prod@localhost/resume_service"
)
```

### 5.2 环境变量配置

创建 `.env` 文件：
```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=resume_service
DB_USER=resume_user
DB_PASSWORD=Resume123!@#Prod

# 应用配置
ENVIRONMENT=production
DEBUG=false
```

## 6. 数据备份策略

### 6.1 创建备份脚本

创建 `backup_database.sh`：
```bash
#!/bin/bash
BACKUP_DIR="/var/backups/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="resume_service"
DB_USER="resume_user"
DB_PASS="Resume123!@#Prod"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/resume_service_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/resume_service_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "数据库备份完成: resume_service_$DATE.sql.gz"
```

### 6.2 设置定时备份

添加到crontab：
```bash
# 编辑crontab
crontab -e

# 添加每日凌晨2点备份
0 2 * * * /path/to/backup_database.sh >> /var/log/mysql_backup.log 2>&1
```

## 7. 监控和维护

### 7.1 性能监控

创建监控脚本 `monitor_db.sh`：
```bash
#!/bin/bash
# 检查MySQL进程
if ! pgrep mysqld > /dev/null; then
    echo "MySQL服务未运行！"
    sudo systemctl start mysql
fi

# 检查连接数
CONNECTIONS=$(mysql -u resume_user -pResume123!@#Prod -e "SHOW STATUS LIKE 'Threads_connected';" | awk 'NR==2{print $2}')
echo "当前连接数: $CONNECTIONS"

# 检查慢查询
SLOW_QUERIES=$(mysql -u resume_user -pResume123!@#Prod -e "SHOW STATUS LIKE 'Slow_queries';" | awk 'NR==2{print $2}')
echo "慢查询数量: $SLOW_QUERIES"
```

### 7.2 日志轮转配置

创建 `/etc/logrotate.d/mysql-resume`：
```
/var/log/mysql/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 640 mysql mysql
    postrotate
        if test -x /usr/bin/mysqladmin && \
           /usr/bin/mysqladmin ping &>/dev/null
        then
           /usr/bin/mysqladmin flush-logs
        fi
    endscript
}
```

## 8. 安全加固

### 8.1 防火墙配置
```bash
# 只允许本地连接MySQL
sudo ufw allow from 127.0.0.1 to any port 3306
sudo ufw deny 3306
```

### 8.2 文件权限设置
```bash
# 设置MySQL配置文件权限
sudo chmod 644 /etc/mysql/mysql.conf.d/mysqld.cnf
sudo chown root:root /etc/mysql/mysql.conf.d/mysqld.cnf

# 设置数据目录权限
sudo chmod 750 /var/lib/mysql
sudo chown mysql:mysql /var/lib/mysql
```

## 9. 故障排除

### 9.1 常见问题

1. **连接被拒绝**
   - 检查MySQL服务状态：`sudo systemctl status mysql`
   - 检查端口监听：`netstat -tlnp | grep 3306`

2. **权限错误**
   - 验证用户权限：`SHOW GRANTS FOR 'resume_user'@'localhost';`
   - 重新授权：重新执行用户创建步骤

3. **性能问题**
   - 检查慢查询日志：`tail -f /var/log/mysql/slow.log`
   - 分析查询性能：使用 `EXPLAIN` 语句

### 9.2 紧急恢复

数据恢复步骤：
```bash
# 停止应用服务
sudo systemctl stop your-app-service

# 恢复数据库
mysql -u resume_user -p resume_service < /var/backups/mysql/resume_service_YYYYMMDD_HHMMSS.sql

# 重启应用服务
sudo systemctl start your-app-service
```

## 10. 部署检查清单

- [ ] MySQL 8.0 安装完成
- [ ] 安全配置已执行
- [ ] 数据库和用户已创建
- [ ] 表结构迁移完成
- [ ] 应用配置已更新
- [ ] 备份策略已实施
- [ ] 监控脚本已部署
- [ ] 安全加固已完成
- [ ] 连接测试通过
- [ ] 性能测试通过

## 11. 联系信息

如遇到问题，请联系：
- 数据库管理员：[联系方式]
- 应用开发团队：[联系方式]
- 运维团队：[联系方式]
