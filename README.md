# 简历渲染服务

这是一个用于渲染和导出简历的服务，支持HTML预览和PDF下载功能。

## 功能特点

- 多模板支持：可选择不同的简历模板
- 主题自定义：支持自定义主题颜色
- HTML预览：生成简历的HTML预览
- PDF导出：将简历导出为高质量PDF文件
- RESTful API：提供完整的API接口

## 系统架构

系统由两部分组成：

1. **FastAPI应用** - 主服务，提供API接口，处理模板渲染
2. **PDF转换服务** - 基于Node.js和Puppeteer的PDF生成服务

## 安装要求

### 主服务
- Python 3.8+
- FastAPI
- Jinja2
- Requests

### PDF服务
- Node.js 14+
- npm
- Puppeteer
- Express

## 快速开始

### 1. 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt

# 安装Node.js依赖
cd pdf-service
npm install
```

### 2. 启动服务

```bash
# 启动PDF服务
cd pdf-service
npm start

# 启动FastAPI服务
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 3. 测试服务状态

```bash
# 测试PDF服务状态
python test_pdf_service_health.py

# 测试所有功能
python test_resume_api.py
```

## API接口

### 获取模板列表

```
GET /resume/templates
```

### 渲染简历HTML

```
POST /resume/render?template_name={template}&theme_color={color}
```

### 预览简历

```
POST /resume/preview?template_name={template}&theme_color={color}
```

### 导出PDF

```
POST /resume/export-pdf?template_name={template}&theme_color={color}&filename={name.pdf}
```

## 环境变量

可以通过环境变量配置服务：

- `PDF_SERVICE_URL`: PDF服务的URL (默认: http://localhost:3001)
- `PORT`: PDF服务的端口 (默认: 3001)

## 故障排除

如果PDF导出功能无法正常工作：

1. 检查PDF服务是否正在运行：`python test_pdf_service_health.py`
2. 确保Node.js和npm已正确安装
3. 检查网络连接和防火墙设置
4. 检查日志获取详细错误信息

## 自定义模板

可以在`app/templates`目录下添加新的Jinja2模板。模板需要使用以下变量：

- `resume`: 包含简历数据的对象
- `theme`: 包含主题设置的对象

## 项目结构

```
.
├── app/
│   ├── __init__.py
│   ├── models/
│   │   └── __init__.py
│   ├── routers/
│   │   ├── __init__.py
│   │   ├── api.py
│   │   └── resume.py
│   ├── schemas/
│   │   ├── __init__.py
│   │   ├── item.py
│   │   └── resume.py
│   ├── services/
│   │   ├── __init__.py
│   │   └── resume_renderer.py
│   ├── templates/
│   │   └── default_template.html
│   └── examples/
│       └── resume_example.py
├── main.py
├── requirements.txt
├── test_resume_api.py
└── README.md
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 运行服务

```bash
uvicorn main:app --reload
```

或者直接运行:

```bash
python main.py
```

## API文档

服务启动后，访问以下URL查看API文档:

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 可用API端点

- `GET /`: 主页
- `GET /api/items`: 获取所有项目
- `GET /api/items/{item_id}`: 获取指定ID的项目
- `POST /api/items`: 创建新项目

### 简历渲染API

- `POST /resume/render`: 接收简历数据，渲染成HTML并返回
- `POST /resume/preview`: 接收简历数据，返回渲染后的HTML和处理后的数据

## 测试简历渲染功能

可以使用提供的测试脚本测试简历渲染功能：

```bash
python test_resume_api.py
```

成功后，可以在`output`目录查看生成的HTML文件。

## 简历数据格式

简历数据格式的定义位于`app/schemas/resume.py`文件中，主要包含以下部分：

- 基本信息(basicInfo)
- 求职意向(jobIntention)
- 教育经历(education)
- 工作经验(work)
- 项目经验(project)
- 技能特长(skills)
- 获奖情况(awards)
- 兴趣爱好(interests)
- 自我评价(evaluation)
- 自定义模块(custom)

示例数据可以参考`app/examples/resume_example.py`。 