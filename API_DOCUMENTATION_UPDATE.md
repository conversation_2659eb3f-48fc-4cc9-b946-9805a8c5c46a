# 微信小程序后端API接口文档更新

## 概述

本文档记录了后端服务的重要调整，主要包括：
1. 用户操作记录机制的重新设计
2. 会员权益操作记录的扩展
3. 前端主动上报机制的实现

## 重要变更

### 1. 用户操作记录机制调整

**变更说明：**
- 从服务端自动记录改为前端主动上报
- 只有用户真正完成操作（如下载PDF、保存照片）后才记录权益使用
- 扩展了UserAction模型，支持更丰富的操作记录

**影响：**
- 前端需要在用户完成操作后主动调用上报接口
- 服务端不再自动扣减会员配额，改为基于前端上报

### 2. 新增接口

#### 2.1 用户操作上报接口

**接口地址：** `POST /user/action/report`

**功能：** 前端主动上报用户操作，记录权益使用

**请求参数：**
```json
{
  "action_type": "download_pdf",  // 必填：操作类型
  "action_content": {},           // 可选：操作详细内容
  "feature_name": "resume_export", // 可选：功能名称
  "resource_type": "pdf",         // 可选：资源类型
  "resource_id": "file_123",      // 可选：资源ID
  "file_size": 1024000,           // 可选：文件大小（字节）
  "file_format": "pdf",           // 可选：文件格式
  "operation_status": "completed", // 可选：操作状态，默认completed
  "error_message": null,          // 可选：错误信息
  "consumed_quota": 1,            // 可选：消耗配额，默认1
  "client_info": {                // 可选：客户端信息
    "device_type": "mobile",
    "app_version": "1.0.0"
  },
  "ip_address": "***********",    // 可选：用户IP
  "template_id": "template_001"   // 可选：模板ID（兼容字段）
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "用户操作记录成功",
  "action_id": 12345,
  "is_member_action": true,
  "consumed_quota": 1
}
```

**操作类型说明：**
- `download_pdf`: PDF下载（会员权益操作）
- `export_jpeg`: JPEG导出（会员权益操作）
- `generate_idphoto`: 证件照生成（会员权益操作）
- `use_premium_template`: 使用高级模板（会员权益操作）
- `watermark_free_export`: 无水印导出（会员权益操作）
- `preview_resume`: 简历预览（普通操作）
- `use_template`: 使用模板（普通操作）

#### 2.2 操作权限验证接口

**接口地址：** `POST /user/action/validate-permission`

**功能：** 前端在执行操作前验证用户权限

**请求参数：**
```json
{
  "action_type": "download_pdf",  // 必填：操作类型
  "check_quota": true             // 可选：是否检查配额，默认true
}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "has_permission": true,
    "is_member_action": true,
    "feature": "resume_export",
    "reason": "今日还可使用5次",
    "usage_info": {
      "limit": 10,
      "used": 5,
      "remaining": 5
    },
    "is_member": true
  },
  "message": "权限验证完成"
}
```

#### 2.3 用户操作统计接口

**接口地址：** `GET /user/action/stats?days=30`

**功能：** 获取用户操作统计信息

**响应示例：**
```json
{
  "success": true,
  "data": {
    "period_days": 30,
    "start_date": "2025-06-19",
    "end_date": "2025-07-19",
    "total_actions": 25,
    "member_actions": 15,
    "feature_stats": {
      "resume_export": {
        "count": 10,
        "total_quota": 10
      },
      "idphoto_generate": {
        "count": 5,
        "total_quota": 5
      }
    },
    "status_stats": {
      "completed": 23,
      "failed": 2
    }
  },
  "message": "获取统计信息成功"
}
```

## 前端集成指南

### 1. 操作流程调整

**旧流程：**
1. 前端调用API（如导出PDF）
2. 服务端自动检查权限并扣减配额
3. 返回结果

**新流程：**
1. 前端调用权限验证接口（可选，用于提前提示用户）
2. 前端调用业务API（如导出PDF）
3. 用户真正完成操作后（如下载完成、保存成功）
4. 前端调用操作上报接口记录权益使用

### 2. 关键实现点

#### 2.1 PDF导出场景
```javascript
// 1. 调用PDF导出API
const response = await fetch('/resume/export-pdf', {
  method: 'POST',
  body: JSON.stringify({
    resume_data: resumeData,
    template_id: templateId,
    theme_config: themeConfig
  })
});

if (response.ok) {
  // 2. 用户下载PDF文件
  const blob = await response.blob();
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'resume.pdf';
  a.click();
  
  // 3. 下载完成后上报操作
  await fetch('/user/action/report', {
    method: 'POST',
    body: JSON.stringify({
      action_type: 'download_pdf',
      feature_name: 'resume_export',
      resource_type: 'pdf',
      file_format: 'pdf',
      template_id: templateId,
      action_content: {
        template_id: templateId,
        theme_config: themeConfig
      }
    })
  });
}
```

#### 2.2 证件照生成场景
```javascript
// 1. 调用证件照生成API
const response = await fetch('/idphoto/generate', {
  method: 'POST',
  body: formData
});

if (response.ok) {
  const result = await response.json();
  
  // 2. 用户保存照片后上报操作
  // 这里可以在用户点击保存按钮时触发
  await fetch('/user/action/report', {
    method: 'POST',
    body: JSON.stringify({
      action_type: 'generate_idphoto',
      feature_name: 'idphoto_generate',
      resource_type: 'idphoto',
      file_format: 'jpeg',
      action_content: {
        size: size,
        color: color,
        filename: originalFilename
      }
    })
  });
}
```

### 3. 错误处理

前端需要处理以下错误情况：
1. 权限不足：提示用户升级会员
2. 配额用完：提示用户明日再试或升级会员
3. 上报失败：可以重试或记录日志

### 4. 用户体验优化

1. **预检查权限**：在用户操作前调用权限验证接口，提前提示
2. **配额显示**：显示用户剩余配额信息
3. **操作统计**：显示用户的使用统计

## 数据库变更

### UserAction表新增字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| feature_name | VARCHAR(50) | 功能名称 |
| resource_type | VARCHAR(30) | 资源类型 |
| resource_id | VARCHAR(100) | 资源ID |
| file_size | INT | 文件大小 |
| file_format | VARCHAR(20) | 文件格式 |
| operation_status | VARCHAR(20) | 操作状态 |
| error_message | TEXT | 错误信息 |
| is_member_action | BOOLEAN | 是否会员操作 |
| consumed_quota | INT | 消耗配额 |
| client_info | JSON | 客户端信息 |
| ip_address | VARCHAR(45) | IP地址 |
| updated_at | TIMESTAMP | 更新时间 |

## 注意事项

1. **向后兼容**：保留了原有的template_id字段和相关接口
2. **权限检查**：服务端仍会检查权限，但不自动扣减配额
3. **数据一致性**：建议前端在关键操作完成后立即上报
4. **错误恢复**：如果上报失败，可以实现重试机制

## 快速集成示例

### 微信小程序端示例代码

```javascript
// utils/api.js - API工具类
class UserActionAPI {
  // 验证操作权限
  static async validatePermission(actionType, checkQuota = true) {
    try {
      const response = await wx.request({
        url: `${API_BASE_URL}/user/action/validate-permission`,
        method: 'POST',
        header: {
          'Authorization': `Bearer ${getToken()}`
        },
        data: {
          action_type: actionType,
          check_quota: checkQuota
        }
      });
      return response.data;
    } catch (error) {
      console.error('权限验证失败:', error);
      return { success: false, message: '权限验证失败' };
    }
  }

  // 上报用户操作
  static async reportAction(actionData) {
    try {
      const response = await wx.request({
        url: `${API_BASE_URL}/user/action/report`,
        method: 'POST',
        header: {
          'Authorization': `Bearer ${getToken()}`
        },
        data: actionData
      });
      return response.data;
    } catch (error) {
      console.error('操作上报失败:', error);
      return { success: false, message: '操作上报失败' };
    }
  }
}

// pages/resume/export.js - 简历导出页面
Page({
  async exportPDF() {
    // 1. 预检查权限（可选）
    const permissionCheck = await UserActionAPI.validatePermission('download_pdf');
    if (!permissionCheck.success || !permissionCheck.data.has_permission) {
      wx.showModal({
        title: '权限不足',
        content: permissionCheck.data.reason || '需要会员权限',
        showCancel: false
      });
      return;
    }

    // 2. 调用PDF导出API
    wx.showLoading({ title: '生成中...' });
    try {
      const response = await wx.request({
        url: `${API_BASE_URL}/resume/export-pdf`,
        method: 'POST',
        responseType: 'arraybuffer',
        data: {
          resume_data: this.data.resumeData,
          template_id: this.data.templateId,
          theme_config: this.data.themeConfig
        }
      });

      if (response.statusCode === 200) {
        // 3. 保存文件
        const filePath = await this.saveFile(response.data);

        // 4. 上报操作
        await UserActionAPI.reportAction({
          action_type: 'download_pdf',
          feature_name: 'resume_export',
          resource_type: 'pdf',
          file_format: 'pdf',
          template_id: this.data.templateId,
          action_content: {
            template_id: this.data.templateId,
            file_path: filePath
          }
        });

        wx.showToast({ title: '导出成功', icon: 'success' });
      }
    } catch (error) {
      wx.showToast({ title: '导出失败', icon: 'error' });
    } finally {
      wx.hideLoading();
    }
  },

  async saveFile(arrayBuffer) {
    return new Promise((resolve, reject) => {
      const fs = wx.getFileSystemManager();
      const fileName = `resume_${Date.now()}.pdf`;
      const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`;

      fs.writeFile({
        filePath,
        data: arrayBuffer,
        success: () => resolve(filePath),
        fail: reject
      });
    });
  }
});
```

## 测试建议

1. 测试权限验证接口的各种场景
2. 测试操作上报接口的成功和失败情况
3. 验证会员配额的正确扣减
4. 测试统计接口的数据准确性

## 联系方式

如有疑问，请联系后端开发团队。
