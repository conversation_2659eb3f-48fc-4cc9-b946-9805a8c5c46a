# nvm


你说得非常对！由 `root` 用户安装 Node.js 并在系统范围内共享，而由普通用户（如 `zhanw`）来使用、安装包和运行程序，这确实是**导致权限问题和管理混乱的常见根源**。

**强烈推荐**你（作为 `zhanw` 用户）自己再安装和管理一个 Node.js 环境。这是最佳实践，可以完全避免权限问题，并且让你能够：

*   **无需 `sudo` 安装 Node 包**：无论是本地项目依赖 (`npm install`) 还是全局工具 (`npm install -g`)，都安装在你的用户目录下，由你完全控制。
*   **轻松管理不同 Node.js 版本**：你可以根据项目需要切换不同版本的 Node.js。
*   **保持系统环境干净**：不会影响系统或其他用户可能依赖的 `root` 安装的 Node.js（如果有的话）。

**最佳方法：使用 Node Version Manager (NVM)**

NVM 是一个非常流行的工具，专门用于在用户级别管理多个 Node.js 版本。

**安装步骤（以 `zhanw` 用户身份运行）：**

1.  **下载并执行 NVM 安装脚本**：
    打开你的终端，运行以下命令之一（通常第一个就可以，如果不行尝试第二个）：
    ```bash
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash
    ```
    或者
    ```bash
    wget -qO- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash
    ```
    *注意：请检查 [NVM GitHub 仓库](https://github.com/nvm-sh/nvm) 获取最新的版本号和安装命令。*

2.  **使 NVM 命令生效**：
    安装脚本通常会提示你关闭并重新打开终端，或者运行类似以下的命令来加载 NVM 到当前会话：
    ```bash
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
    [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion
    ```
    它也会自动将这些命令添加到你的 shell 配置文件（如 `~/.bashrc`, `~/.zshrc`）中，以便下次登录时自动加载。你可以检查一下你的配置文件末尾是否有 NVM相关的行。

3.  **验证 NVM 安装**：
    关闭并重新打开终端，然后运行：
    ```bash
    command -v nvm
    ```
    如果显示 `nvm`，说明安装成功。

4.  **安装 Node.js**：
    现在你可以使用 NVM 安装 Node.js 了。推荐安装最新的 LTS (长期支持) 版本，例如 Node.js 20.x 或 22.x（在写这篇回答时）：
    ```bash
    nvm install 20 # 或者 nvm install 22
    ```
    或者安装特定版本：
    ```bash
    nvm install 20.11.1
    ```
    NVM 会下载 Node.js 并将其安装到 `~/.nvm/versions/node/` 目录下。

5.  **设置默认 Node.js 版本**（可选但推荐）：
    ```bash
    nvm alias default 20 # 将默认版本设为你安装的版本
    ```

6.  **验证 Node.js 安装**：
    ```bash
    node -v
    npm -v
    which node # 这应该显示类似 /home/<USER>/.nvm/versions/node/v20.x.x/bin/node 的路径
    which npm # 这应该显示类似 /home/<USER>/.nvm/versions/node/v20.x.x/bin/npm 的路径
    ```
    如果 `which node` 显示的是 NVM 目录下的路径，那么一切设置正确！

**使用 NVM 后的工作流程：**

*   **启动程序**：直接运行 `node your_script.js`。系统会使用 NVM 管理的、你当前激活的 Node.js 版本。
*   **安装本地包**：在你的项目目录中，直接运行 `npm install <package_name>`，无需 `sudo`。包会安装到项目的 `node_modules` 目录下。
*   **安装全局包**：直接运行 `npm install -g <package_name>`，无需 `sudo`。全局包会安装到当前 Node.js 版本对应的 NVM 目录下（例如 `~/.nvm/versions/node/v20.x.x/lib/node_modules`），只有你的用户可以使用。

**关于系统（root）安装的 Node.js：**

*   它仍然存在于系统中。
*   但由于 NVM 修改了你的 `PATH` 环境变量，将 NVM 的路径放在了前面，所以当你作为 `zhanw` 用户运行 `node` 或 `npm` 时，会优先使用 NVM 管理的版本。
*   你（`zhanw` 用户）不需要也不应该去动 `root` 安装的 Node.js。如果系统管理员确认它不再被任何系统服务需要，可以由管理员考虑卸载，但这与你的用户环境无关。

总之，强烈建议你使用 NVM 为 `zhanw` 用户建立一个独立的 Node.js 环境。这将彻底解决你遇到的权限和管理问题。


# 下载无头浏览器
PUPPETEER_CACHE_DIR=./.local-chromium npm install puppeteer@latest
