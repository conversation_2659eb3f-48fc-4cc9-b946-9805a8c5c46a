"""
数据库配置和连接管理
"""
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from config.fastapi_config import settings

# 创建数据库引擎
engine = create_engine(
    settings.DATABASE_URL,
    echo=settings.DEBUG,  # 开发环境下显示SQL语句
    pool_pre_ping=True,  # 连接池预检查
    pool_recycle=3600,   # 连接回收时间（1小时）
    pool_size=20,        # 连接池大小
    max_overflow=30,     # 超出连接池大小的最大连接数
    pool_timeout=30,     # 获取连接的超时时间（秒）
    pool_reset_on_return='commit',  # 连接返回时重置状态
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()

# 依赖注入：获取数据库会话
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 获取连接池状态信息
def get_pool_status():
    """获取数据库连接池状态"""
    pool = engine.pool
    return {
        "pool_size": pool.size(),
        "checked_in": pool.checkedin(),
        "checked_out": pool.checkedout(),
        "overflow": pool.overflow(),
        "total_connections": pool.size() + pool.overflow(),
        "available_connections": pool.checkedin(),
        "busy_connections": pool.checkedout(),
        "pool_status": "healthy" if pool.checkedout() < (pool.size() + pool.overflow()) else "overloaded"
    }
