"""
支付相关定时任务
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Optional

from app.services.callback_handler import callback_handler

logger = logging.getLogger(__name__)


class PaymentTaskScheduler:
    """支付任务调度器"""
    
    def __init__(self):
        self.is_running = False
        self.tasks = []
    
    async def start(self):
        """启动定时任务"""
        if self.is_running:
            logger.warning("支付任务调度器已在运行")
            return
        
        self.is_running = True
        logger.info("启动支付任务调度器")
        
        # 启动各种定时任务
        self.tasks = [
            asyncio.create_task(self._timeout_order_task()),
            asyncio.create_task(self._cleanup_logs_task()),
            asyncio.create_task(self._sync_membership_status_task())
        ]
        
        # 等待所有任务完成（实际上是无限循环）
        try:
            await asyncio.gather(*self.tasks)
        except asyncio.CancelledError:
            logger.info("支付任务调度器已停止")
    
    async def stop(self):
        """停止定时任务"""
        if not self.is_running:
            return
        
        self.is_running = False
        logger.info("停止支付任务调度器")
        
        # 取消所有任务
        for task in self.tasks:
            task.cancel()
        
        # 等待任务取消完成
        await asyncio.gather(*self.tasks, return_exceptions=True)
        self.tasks.clear()
    
    async def _timeout_order_task(self):
        """超时订单处理任务"""
        logger.info("启动超时订单处理任务")
        
        while self.is_running:
            try:
                await callback_handler.handle_timeout_orders()
                # 每5分钟检查一次
                await asyncio.sleep(300)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"超时订单处理任务异常: {e}")
                # 出错后等待1分钟再继续
                await asyncio.sleep(60)
    
    async def _cleanup_logs_task(self):
        """清理日志任务"""
        logger.info("启动日志清理任务")
        
        while self.is_running:
            try:
                await self._cleanup_old_logs()
                # 每天凌晨2点执行一次
                await self._wait_until_next_cleanup()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"日志清理任务异常: {e}")
                # 出错后等待1小时再继续
                await asyncio.sleep(3600)
    
    async def _sync_membership_status_task(self):
        """同步会员状态任务"""
        logger.info("启动会员状态同步任务")
        
        while self.is_running:
            try:
                await self._sync_expired_memberships()
                # 每小时执行一次
                await asyncio.sleep(3600)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"会员状态同步任务异常: {e}")
                # 出错后等待30分钟再继续
                await asyncio.sleep(1800)
    
    async def _cleanup_old_logs(self):
        """清理旧日志"""
        from app.database import SessionLocal
        from app.models.payment import PaymentLog
        from sqlalchemy import and_
        
        db = SessionLocal()
        try:
            # 删除30天前的日志
            cutoff_date = datetime.now() - timedelta(days=30)
            
            deleted_count = db.query(PaymentLog).filter(
                PaymentLog.created_at < cutoff_date
            ).delete()
            
            db.commit()
            
            if deleted_count > 0:
                logger.info(f"清理了 {deleted_count} 条旧的支付日志")
            
        except Exception as e:
            db.rollback()
            logger.error(f"清理旧日志异常: {e}")
        finally:
            db.close()
    
    async def _sync_expired_memberships(self):
        """同步过期会员状态"""
        from app.database import SessionLocal
        from app.models import User
        from app.models.payment import UserMembership
        from sqlalchemy import and_
        
        db = SessionLocal()
        try:
            # 查找已过期但仍标记为活跃的会员记录
            expired_memberships = db.query(UserMembership).filter(
                and_(
                    UserMembership.is_active == True,
                    UserMembership.end_date <= datetime.now()
                )
            ).all()
            
            if not expired_memberships:
                return
            
            logger.info(f"发现 {len(expired_memberships)} 个过期会员记录")
            
            # 更新过期会员状态
            for membership in expired_memberships:
                membership.is_active = False
                membership.deactivated_at = datetime.now()
                
                # 检查用户是否还有其他有效会员记录
                active_membership = db.query(UserMembership).filter(
                    and_(
                        UserMembership.user_id == membership.user_id,
                        UserMembership.is_active == True,
                        UserMembership.end_date > datetime.now()
                    )
                ).first()
                
                # 如果没有其他有效会员记录，更新用户会员状态
                if not active_membership:
                    user = db.query(User).filter(User.id == membership.user_id).first()
                    if user:
                        user.is_member = False
                        logger.info(f"用户 {user.id} 会员状态已过期")
            
            db.commit()
            logger.info(f"同步了 {len(expired_memberships)} 个过期会员状态")
            
        except Exception as e:
            db.rollback()
            logger.error(f"同步过期会员状态异常: {e}")
        finally:
            db.close()
    
    async def _wait_until_next_cleanup(self):
        """等待到下次清理时间（凌晨2点）"""
        now = datetime.now()
        
        # 计算下次凌晨2点的时间
        next_cleanup = now.replace(hour=2, minute=0, second=0, microsecond=0)
        if next_cleanup <= now:
            # 如果今天的2点已过，设置为明天的2点
            next_cleanup += timedelta(days=1)
        
        # 计算等待时间
        wait_seconds = (next_cleanup - now).total_seconds()
        logger.info(f"下次日志清理时间: {next_cleanup}, 等待 {wait_seconds/3600:.1f} 小时")
        
        await asyncio.sleep(wait_seconds)


# 创建全局实例
payment_task_scheduler = PaymentTaskScheduler()


async def start_payment_tasks():
    """启动支付相关定时任务"""
    await payment_task_scheduler.start()


async def stop_payment_tasks():
    """停止支付相关定时任务"""
    await payment_task_scheduler.stop()


# 手动执行任务的函数
async def manual_cleanup_timeout_orders():
    """手动清理超时订单"""
    logger.info("手动执行超时订单清理")
    await callback_handler.handle_timeout_orders()


async def manual_cleanup_logs():
    """手动清理日志"""
    logger.info("手动执行日志清理")
    await payment_task_scheduler._cleanup_old_logs()


async def manual_sync_memberships():
    """手动同步会员状态"""
    logger.info("手动执行会员状态同步")
    await payment_task_scheduler._sync_expired_memberships()
