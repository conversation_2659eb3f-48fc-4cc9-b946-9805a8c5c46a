"""
会员权益管理服务
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from app.models import User, UserAction
from app.models.payment import UserMembership, MembershipPlan
from app.database import get_db

logger = logging.getLogger(__name__)


class MembershipService:
    """会员权益管理服务"""
    
    # 会员权益配置
    MEMBER_BENEFITS = {
        "resume_export": {
            "name": "简历导出",
            "free_limit": 3,  # 非会员每日限制
            "member_limit": -1,  # 会员无限制 (-1表示无限制)
            "description": "导出PDF和图片格式简历"
        },
        "idphoto_generate": {
            "name": "证件照生成",
            "free_limit": 2,  # 非会员每日限制
            "member_limit": -1,  # 会员无限制
            "description": "生成各种规格的证件照"
        },
        "premium_templates": {
            "name": "高级模板",
            "free_limit": 0,  # 非会员不能使用
            "member_limit": -1,  # 会员无限制
            "description": "使用高级简历模板"
        },
        "priority_support": {
            "name": "优先客服",
            "free_limit": 0,  # 非会员不享受
            "member_limit": 1,  # 会员享受
            "description": "优先客服支持"
        },
        "watermark_free": {
            "name": "无水印导出",
            "free_limit": 0,  # 非会员有水印
            "member_limit": 1,  # 会员无水印
            "description": "导出文件无水印"
        }
    }
    
    def get_user_membership_info(self, db: Session, user_id: int) -> Dict[str, Any]:
        """获取用户会员信息"""
        try:
            # 获取当前有效的会员信息
            current_membership = db.query(UserMembership).filter(
                UserMembership.user_id == user_id,
                UserMembership.is_active == True,
                UserMembership.end_date > datetime.now()
            ).first()
            
            if not current_membership:
                return {
                    "is_member": False,
                    "membership_type": "free",
                    "plan_name": "免费用户",
                    "end_date": None,
                    "remaining_days": 0,
                    "features": []
                }
            
            # 获取套餐信息
            plan = db.query(MembershipPlan).filter(
                MembershipPlan.id == current_membership.plan_id
            ).first()
            
            remaining_days = (current_membership.end_date - datetime.now()).days
            remaining_days = max(0, remaining_days)
            
            return {
                "is_member": True,
                "membership_type": "premium",
                "plan_name": plan.name if plan else "会员用户",
                "plan_id": current_membership.plan_id,
                "end_date": current_membership.end_date,
                "remaining_days": remaining_days,
                "features": plan.features if plan else [],
                "auto_renew": current_membership.auto_renew
            }
            
        except Exception as e:
            logger.error(f"获取用户会员信息异常: {user_id}, {e}")
            return {
                "is_member": False,
                "membership_type": "free",
                "plan_name": "免费用户",
                "end_date": None,
                "remaining_days": 0,
                "features": []
            }
    
    def check_feature_permission(
        self, 
        db: Session, 
        user_id: int, 
        feature: str,
        check_usage: bool = True
    ) -> Dict[str, Any]:
        """检查功能权限"""
        try:
            # 获取会员信息
            membership_info = self.get_user_membership_info(db, user_id)
            is_member = membership_info["is_member"]
            
            # 检查功能是否存在
            if feature not in self.MEMBER_BENEFITS:
                return {
                    "has_permission": False,
                    "reason": "未知功能",
                    "is_member": is_member,
                    "usage_info": {}
                }
            
            benefit_config = self.MEMBER_BENEFITS[feature]
            
            # 获取使用限制
            if is_member:
                limit = benefit_config["member_limit"]
            else:
                limit = benefit_config["free_limit"]
            
            # 如果限制为0，表示不允许使用
            if limit == 0:
                return {
                    "has_permission": False,
                    "reason": "需要会员权限" if not is_member else "功能不可用",
                    "is_member": is_member,
                    "upgrade_required": not is_member,
                    "usage_info": {
                        "limit": limit,
                        "used": 0,
                        "remaining": 0
                    }
                }
            
            # 如果限制为-1，表示无限制
            if limit == -1:
                return {
                    "has_permission": True,
                    "reason": "无限制使用",
                    "is_member": is_member,
                    "usage_info": {
                        "limit": -1,
                        "used": 0,
                        "remaining": -1
                    }
                }
            
            # 检查今日使用量
            if check_usage:
                usage_info = self._get_daily_usage(db, user_id, feature)
                used_count = usage_info["used"]
                remaining = max(0, limit - used_count)
                
                return {
                    "has_permission": remaining > 0,
                    "reason": "今日使用次数已达上限" if remaining == 0 else f"今日还可使用{remaining}次",
                    "is_member": is_member,
                    "upgrade_required": not is_member and remaining == 0,
                    "usage_info": {
                        "limit": limit,
                        "used": used_count,
                        "remaining": remaining
                    }
                }
            else:
                return {
                    "has_permission": True,
                    "reason": f"每日可使用{limit}次",
                    "is_member": is_member,
                    "usage_info": {
                        "limit": limit,
                        "used": 0,
                        "remaining": limit
                    }
                }
                
        except Exception as e:
            logger.error(f"检查功能权限异常: {user_id}, {feature}, {e}")
            return {
                "has_permission": False,
                "reason": "权限检查失败",
                "is_member": False,
                "usage_info": {}
            }
    
    def _get_daily_usage(self, db: Session, user_id: int, feature: str) -> Dict[str, Any]:
        """获取今日使用量"""
        try:
            # 计算今日开始时间
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            
            # 映射功能到行为类型
            action_type_mapping = {
                "resume_export": ["export_pdf", "export_jpeg"],
                "idphoto_generate": ["generate_idphoto"],
                "premium_templates": ["use_premium_template"],
                "priority_support": ["priority_support"],
                "watermark_free": ["watermark_free_export"]
            }
            
            action_types = action_type_mapping.get(feature, [feature])
            
            # 查询今日使用次数
            used_count = db.query(func.count(UserAction.id)).filter(
                and_(
                    UserAction.user_id == user_id,
                    UserAction.action_type.in_(action_types),
                    UserAction.created_at >= today_start
                )
            ).scalar() or 0
            
            return {
                "used": used_count,
                "date": today_start.date(),
                "action_types": action_types
            }
            
        except Exception as e:
            logger.error(f"获取今日使用量异常: {user_id}, {feature}, {e}")
            return {"used": 0, "date": datetime.now().date(), "action_types": []}
    
    def record_feature_usage(
        self, 
        db: Session, 
        user_id: int, 
        feature: str,
        action_content: Optional[Dict[str, Any]] = None,
        template_id: Optional[str] = None
    ) -> bool:
        """记录功能使用"""
        try:
            # 映射功能到行为类型
            action_type_mapping = {
                "resume_export": "export_pdf",  # 或 export_jpeg
                "idphoto_generate": "generate_idphoto",
                "premium_templates": "use_premium_template",
                "priority_support": "priority_support",
                "watermark_free": "watermark_free_export"
            }
            
            action_type = action_type_mapping.get(feature, feature)
            
            # 创建用户行为记录
            user_action = UserAction(
                user_id=user_id,
                action_type=action_type,
                action_content=action_content or {"feature": feature},
                template_id=template_id
            )
            
            db.add(user_action)
            db.commit()
            
            logger.info(f"记录功能使用: 用户{user_id}, 功能{feature}")
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"记录功能使用异常: {user_id}, {feature}, {e}")
            return False
    
    def get_usage_statistics(
        self, 
        db: Session, 
        user_id: int,
        days: int = 30
    ) -> Dict[str, Any]:
        """获取使用统计"""
        try:
            # 计算统计开始时间
            start_date = datetime.now() - timedelta(days=days)
            
            # 获取会员信息
            membership_info = self.get_user_membership_info(db, user_id)
            
            # 统计各功能使用次数
            feature_stats = {}
            for feature, config in self.MEMBER_BENEFITS.items():
                action_type_mapping = {
                    "resume_export": ["export_pdf", "export_jpeg"],
                    "idphoto_generate": ["generate_idphoto"],
                    "premium_templates": ["use_premium_template"],
                    "priority_support": ["priority_support"],
                    "watermark_free": ["watermark_free_export"]
                }
                
                action_types = action_type_mapping.get(feature, [feature])
                
                count = db.query(func.count(UserAction.id)).filter(
                    and_(
                        UserAction.user_id == user_id,
                        UserAction.action_type.in_(action_types),
                        UserAction.created_at >= start_date
                    )
                ).scalar() or 0
                
                feature_stats[feature] = {
                    "name": config["name"],
                    "count": count,
                    "description": config["description"]
                }
            
            # 计算总使用次数
            total_actions = sum(stat["count"] for stat in feature_stats.values())
            
            return {
                "period_days": days,
                "start_date": start_date.date(),
                "end_date": datetime.now().date(),
                "membership_info": membership_info,
                "feature_stats": feature_stats,
                "total_actions": total_actions
            }
            
        except Exception as e:
            logger.error(f"获取使用统计异常: {user_id}, {e}")
            return {
                "period_days": days,
                "start_date": (datetime.now() - timedelta(days=days)).date(),
                "end_date": datetime.now().date(),
                "membership_info": {"is_member": False},
                "feature_stats": {},
                "total_actions": 0
            }
    
    def get_all_benefits(self) -> Dict[str, Any]:
        """获取所有会员权益配置"""
        return {
            "benefits": self.MEMBER_BENEFITS,
            "description": "会员权益说明",
            "upgrade_benefits": [
                "无限制简历导出",
                "无限制证件照生成", 
                "高级模板使用权限",
                "优先客服支持",
                "无水印导出"
            ]
        }


# 创建全局实例
membership_service = MembershipService()
