"""
临时文件管理服务
"""
import os
import uuid
import logging
import hashlib
import json
from typing import Optional, Dict, Any
from PIL import Image
import io
import threading
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class TempFileService:
    """临时文件管理服务"""

    def __init__(self, temp_dir: str = "static/temp", max_age_hours: int = 24):
        """
        初始化临时文件服务

        Args:
            temp_dir: 临时文件存储目录
            max_age_hours: 文件最大保存时间（小时）
        """
        self.temp_dir = temp_dir
        self.max_age_hours = max_age_hours
        self.file_registry: Dict[str, Dict[str, Any]] = {}
        self.hash_cache: Dict[str, str] = {}  # hash -> file_id 映射
        self._lock = threading.Lock()

        # 确保临时目录存在
        os.makedirs(self.temp_dir, exist_ok=True)

        logger.info(f"临时文件服务初始化，目录: {self.temp_dir}, 最大保存时间: {max_age_hours}小时")

    def _generate_cache_hash(self,
                           resume_data: Dict[str, Any],
                           template_id: str,
                           theme_config: Dict[str, Any],
                           file_type: str,
                           extra_params: Optional[Dict[str, Any]] = None) -> str:
        """
        生成缓存hash

        Args:
            resume_data: 简历数据
            template_id: 模板ID
            theme_config: 主题配置
            file_type: 文件类型 ('image', 'pdf')
            extra_params: 额外参数（如图片质量设置）

        Returns:
            缓存hash字符串
        """
        # 创建用于哈希的数据结构
        hash_data = {
            'resume_data': resume_data,
            'template_id': template_id,
            'theme_config': theme_config,
            'file_type': file_type
        }

        # 添加额外参数
        if extra_params:
            hash_data['extra_params'] = extra_params

        # 序列化并生成哈希
        json_str = json.dumps(hash_data, sort_keys=True, ensure_ascii=False)
        hash_obj = hashlib.sha256(json_str.encode('utf-8'))

        return f"{file_type}_{hash_obj.hexdigest()[:16]}"

    def get_cached_file_url(self,
                           resume_data: Dict[str, Any],
                           template_id: str,
                           theme_config: Dict[str, Any],
                           file_type: str,
                           base_url: str = "",
                           extra_params: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """
        根据请求数据hash查找缓存的文件URL

        Args:
            resume_data: 简历数据
            template_id: 模板ID
            theme_config: 主题配置
            file_type: 文件类型 ('image', 'pdf')
            base_url: 基础URL
            extra_params: 额外参数

        Returns:
            缓存的文件URL，如果不存在返回None
        """
        # 生成hash
        cache_hash = self._generate_cache_hash(
            resume_data, template_id, theme_config, file_type, extra_params
        )

        with self._lock:
            # 检查hash缓存中是否存在
            if cache_hash in self.hash_cache:
                file_id = self.hash_cache[cache_hash]

                # 检查文件是否仍然存在且未过期
                if file_id in self.file_registry:
                    file_info = self.file_registry[file_id]

                    # 检查文件是否过期
                    if self._is_file_expired(file_info['created_at']):
                        # 文件过期，清理缓存
                        self._remove_file_and_hash(file_id, cache_hash)
                        return None

                    # 检查文件是否实际存在
                    if not os.path.exists(file_info['filepath']):
                        # 文件不存在，清理缓存
                        self._remove_file_and_hash(file_id, cache_hash)
                        return None

                    # 增加访问计数
                    file_info['access_count'] += 1
                    file_url = f"{base_url}/static/temp/{file_info['filename']}"
                    logger.info(f"缓存命中: {cache_hash} -> {file_url}")
                    return file_url
                else:
                    # 文件记录不存在，清理hash缓存
                    del self.hash_cache[cache_hash]

        return None

    def save_compressed_image(self,
                            image_data: bytes,
                            quality: int = 85,
                            max_width: int = 1200,
                            max_height: int = 3600,
                            resume_data: Optional[Dict[str, Any]] = None,
                            template_id: Optional[str] = None,
                            theme_config: Optional[Dict[str, Any]] = None) -> str:
        """
        保存并压缩图片

        Args:
            image_data: 原始图片数据
            quality: JPEG压缩质量 (1-100)
            max_width: 最大宽度
            max_height: 最大高度
            resume_data: 简历数据（用于缓存）
            template_id: 模板ID（用于缓存）
            theme_config: 主题配置（用于缓存）

        Returns:
            临时文件的唯一标识符
        """
        try:
            # 生成唯一文件名
            file_id = str(uuid.uuid4())
            filename = f"{file_id}.jpg"
            filepath = os.path.join(self.temp_dir, filename)



            # 使用PIL处理图片
            with Image.open(io.BytesIO(image_data)) as img:
                # 转换为RGB模式（如果不是的话）
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # 计算新的尺寸（优先保持A4宽度，高度弹性调整）
                original_width, original_height = img.size

                # 优先按宽度缩放，保持A4纸的宽度比例
                if original_width > max_width:
                    # 按宽度缩放
                    scale_ratio = max_width / original_width
                    new_width = max_width
                    new_height = int(original_height * scale_ratio)

                    # 如果缩放后高度仍然超过限制，再按高度缩放
                    if new_height > max_height:
                        scale_ratio = max_height / new_height
                        new_width = int(new_width * scale_ratio)
                        new_height = max_height

                    img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                    logger.info(f"图片已缩放: {original_width}x{original_height} -> {new_width}x{new_height}")
                elif original_height > max_height:
                    # 只有高度超限时才按高度缩放
                    scale_ratio = max_height / original_height
                    new_width = int(original_width * scale_ratio)
                    new_height = max_height
                    img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                    logger.info(f"图片已缩放: {original_width}x{original_height} -> {new_width}x{new_height}")
                else:
                    logger.info(f"图片尺寸符合要求，无需缩放: {original_width}x{original_height}")

                # 保存压缩后的图片
                img.save(filepath, 'JPEG', quality=quality, optimize=True)

            # 获取文件信息
            file_size = os.path.getsize(filepath)
            original_size = len(image_data)
            compression_ratio = (1 - file_size / original_size) * 100

            # 记录文件信息
            with self._lock:
                self.file_registry[file_id] = {
                    'filename': filename,
                    'filepath': filepath,
                    'created_at': datetime.now(),
                    'original_size': original_size,
                    'compressed_size': file_size,
                    'compression_ratio': compression_ratio,
                    'access_count': 0,
                    'file_type': 'image'
                }

                # 如果提供了缓存参数，添加hash缓存
                if resume_data and template_id and theme_config:
                    extra_params = {
                        'quality': quality,
                        'max_width': max_width,
                        'max_height': max_height
                    }
                    cache_hash = self._generate_cache_hash(
                        resume_data, template_id, theme_config, 'image', extra_params
                    )
                    self.hash_cache[cache_hash] = file_id
                    self.file_registry[file_id]['cache_hash'] = cache_hash
                    logger.info(f"图片缓存已建立: {cache_hash} -> {file_id}")

            logger.info(f"图片压缩完成: {file_id}, 原始大小: {original_size}字节, "
                       f"压缩后: {file_size}字节, 压缩率: {compression_ratio:.1f}%")

            return file_id

        except Exception as e:
            logger.error(f"保存压缩图片失败: {str(e)}")
            raise

    def save_pdf_file(self,
                     pdf_data: bytes,
                     resume_data: Optional[Dict[str, Any]] = None,
                     template_id: Optional[str] = None,
                     theme_config: Optional[Dict[str, Any]] = None) -> str:
        """
        保存PDF文件

        Args:
            pdf_data: PDF文件数据
            resume_data: 简历数据（用于缓存）
            template_id: 模板ID（用于缓存）
            theme_config: 主题配置（用于缓存）

        Returns:
            临时文件的唯一标识符
        """
        try:
            # 生成唯一文件名
            file_id = str(uuid.uuid4())
            filename = f"{file_id}.pdf"
            filepath = os.path.join(self.temp_dir, filename)

            # 保存PDF文件
            with open(filepath, 'wb') as f:
                f.write(pdf_data)

            # 获取文件信息
            file_size = len(pdf_data)

            # 记录文件信息
            with self._lock:
                self.file_registry[file_id] = {
                    'filename': filename,
                    'filepath': filepath,
                    'created_at': datetime.now(),
                    'original_size': file_size,
                    'compressed_size': file_size,  # PDF不压缩
                    'compression_ratio': 0,
                    'access_count': 0,
                    'file_type': 'pdf'
                }

                # 如果提供了缓存参数，添加hash缓存
                if resume_data and template_id and theme_config:
                    cache_hash = self._generate_cache_hash(
                        resume_data, template_id, theme_config, 'pdf'
                    )
                    self.hash_cache[cache_hash] = file_id
                    self.file_registry[file_id]['cache_hash'] = cache_hash
                    logger.info(f"PDF缓存已建立: {cache_hash} -> {file_id}")

            logger.info(f"PDF文件保存完成: {file_id}, 大小: {file_size}字节")

            return file_id

        except Exception as e:
            logger.error(f"保存PDF文件失败: {str(e)}")
            raise

    def get_file_path(self, file_id: str) -> Optional[str]:
        """
        获取文件路径

        Args:
            file_id: 文件标识符

        Returns:
            文件路径，如果文件不存在返回None
        """
        with self._lock:
            if file_id in self.file_registry:
                file_info = self.file_registry[file_id]

                # 检查文件是否过期
                if self._is_file_expired(file_info['created_at']):
                    self._remove_file(file_id)
                    return None

                # 检查文件是否实际存在
                if not os.path.exists(file_info['filepath']):
                    self._remove_file(file_id)
                    return None

                # 增加访问计数
                file_info['access_count'] += 1
                return file_info['filepath']

        return None

    def get_file_url(self, file_id: str, base_url: str = "") -> Optional[str]:
        """
        获取文件访问URL

        Args:
            file_id: 文件标识符
            base_url: 基础URL

        Returns:
            文件访问URL
        """
        with self._lock:
            if file_id in self.file_registry:
                file_info = self.file_registry[file_id]

                # 检查文件是否过期
                if self._is_file_expired(file_info['created_at']):
                    self._remove_file(file_id)
                    return None

                # 检查文件是否实际存在
                if not os.path.exists(file_info['filepath']):
                    self._remove_file(file_id)
                    return None

                return f"{base_url}/static/temp/{file_info['filename']}"

        return None

    def cleanup_expired_files(self):
        """清理过期文件"""
        expired_files = []

        with self._lock:
            for file_id, file_info in self.file_registry.items():
                if self._is_file_expired(file_info['created_at']):
                    expired_files.append(file_id)

        # 删除过期文件
        for file_id in expired_files:
            self._remove_file(file_id)

        if expired_files:
            logger.info(f"清理了 {len(expired_files)} 个过期文件")

    def get_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        with self._lock:
            total_files = len(self.file_registry)
            total_original_size = sum(info['original_size'] for info in self.file_registry.values())
            total_compressed_size = sum(info['compressed_size'] for info in self.file_registry.values())
            total_access_count = sum(info['access_count'] for info in self.file_registry.values())

            # 统计不同文件类型
            image_files = sum(1 for info in self.file_registry.values() if info.get('file_type') == 'image')
            pdf_files = sum(1 for info in self.file_registry.values() if info.get('file_type') == 'pdf')

            # 统计缓存相关信息
            cached_files = sum(1 for info in self.file_registry.values() if 'cache_hash' in info)
            total_hash_cache = len(self.hash_cache)

            avg_compression_ratio = 0
            if total_files > 0:
                avg_compression_ratio = sum(info['compression_ratio'] for info in self.file_registry.values()) / total_files

            return {
                'total_files': total_files,
                'image_files': image_files,
                'pdf_files': pdf_files,
                'cached_files': cached_files,
                'hash_cache_entries': total_hash_cache,
                'total_original_size': total_original_size,
                'total_compressed_size': total_compressed_size,
                'total_access_count': total_access_count,
                'average_compression_ratio': avg_compression_ratio,
                'storage_saved_bytes': total_original_size - total_compressed_size
            }

    def _is_file_expired(self, created_at: datetime) -> bool:
        """检查文件是否过期"""
        return datetime.now() - created_at > timedelta(hours=self.max_age_hours)

    def _remove_file(self, file_id: str):
        """删除文件（内部方法，需要在锁内调用）"""
        if file_id in self.file_registry:
            file_info = self.file_registry[file_id]
            filepath = file_info['filepath']

            # 删除物理文件
            try:
                if os.path.exists(filepath):
                    os.remove(filepath)
            except Exception as e:
                logger.warning(f"删除文件失败: {filepath}, 错误: {str(e)}")

            # 如果有缓存hash，也要清理
            cache_hash = file_info.get('cache_hash')
            if cache_hash and cache_hash in self.hash_cache:
                del self.hash_cache[cache_hash]

            # 从注册表中移除
            del self.file_registry[file_id]

    def _remove_file_and_hash(self, file_id: str, cache_hash: str):
        """删除文件和对应的hash缓存（内部方法，需要在锁内调用）"""
        if file_id in self.file_registry:
            file_info = self.file_registry[file_id]
            filepath = file_info['filepath']

            # 删除物理文件
            try:
                if os.path.exists(filepath):
                    os.remove(filepath)
                    logger.debug(f"删除文件: {filepath}")
            except Exception as e:
                logger.warning(f"删除文件失败: {filepath}, 错误: {str(e)}")

            # 从注册表中移除
            del self.file_registry[file_id]

        # 清理hash缓存
        if cache_hash in self.hash_cache:
            del self.hash_cache[cache_hash]
            logger.debug(f"清理hash缓存: {cache_hash}")


# 创建全局单例实例
_temp_file_service_instance = None

def get_temp_file_service() -> TempFileService:
    """
    获取全局临时文件服务实例（单例模式）

    Returns:
        TempFileService: 临时文件服务实例
    """
    global _temp_file_service_instance
    if _temp_file_service_instance is None:
        _temp_file_service_instance = TempFileService()
        logger.info("创建全局临时文件服务实例")
    return _temp_file_service_instance
