"""
配置健康检查服务
"""
import os
import logging
from datetime import datetime
from typing import Dict, Any, List, Tuple
from pathlib import Path
import requests

from config.wechat_pay_config import get_wechat_pay_config, validate_wechat_pay_config

logger = logging.getLogger(__name__)


class ConfigHealthCheckService:
    """配置健康检查服务"""
    
    def __init__(self):
        self.checks = [
            self._check_wechat_pay_config,
            self._check_database_config,
            self._check_certificate_files,
            self._check_network_connectivity,
            self._check_environment_variables
        ]
    
    def run_all_checks(self) -> Dict[str, Any]:
        """运行所有健康检查"""
        results = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "healthy",
            "checks": {},
            "summary": {
                "total": len(self.checks),
                "passed": 0,
                "failed": 0,
                "warnings": 0
            }
        }
        
        for check_func in self.checks:
            try:
                check_name = check_func.__name__.replace("_check_", "")
                check_result = check_func()
                results["checks"][check_name] = check_result
                
                # 更新统计
                if check_result["status"] == "pass":
                    results["summary"]["passed"] += 1
                elif check_result["status"] == "fail":
                    results["summary"]["failed"] += 1
                    results["overall_status"] = "unhealthy"
                elif check_result["status"] == "warning":
                    results["summary"]["warnings"] += 1
                    if results["overall_status"] == "healthy":
                        results["overall_status"] = "degraded"
                        
            except Exception as e:
                logger.error(f"健康检查异常: {check_func.__name__}, {e}")
                results["checks"][check_func.__name__] = {
                    "status": "fail",
                    "message": f"检查异常: {str(e)}",
                    "details": {}
                }
                results["summary"]["failed"] += 1
                results["overall_status"] = "unhealthy"
        
        return results
    
    def _check_wechat_pay_config(self) -> Dict[str, Any]:
        """检查微信支付配置"""
        try:
            config = get_wechat_pay_config()
            
            # 检查必需配置项
            required_fields = [
                "appid", "mchid", "api_v3_key", 
                "cert_serial_no", "notify_url"
            ]
            
            missing_fields = []
            for field in required_fields:
                if not config.get(field):
                    missing_fields.append(field)
            
            if missing_fields:
                return {
                    "status": "fail",
                    "message": f"缺少必需的配置项: {', '.join(missing_fields)}",
                    "details": {
                        "missing_fields": missing_fields,
                        "config_complete": False
                    }
                }
            
            # 验证配置
            try:
                validate_wechat_pay_config()
                config_valid = True
                validation_message = "配置验证通过"
            except ValueError as e:
                config_valid = False
                validation_message = str(e)
            
            return {
                "status": "pass" if config_valid else "fail",
                "message": validation_message,
                "details": {
                    "appid": config["appid"][:8] + "****" if config["appid"] else "",
                    "mchid": config["mchid"][:4] + "****" if config["mchid"] else "",
                    "sandbox": config["sandbox"],
                    "notify_url": config["notify_url"],
                    "config_complete": True,
                    "config_valid": config_valid
                }
            }
            
        except Exception as e:
            return {
                "status": "fail",
                "message": f"微信支付配置检查失败: {str(e)}",
                "details": {}
            }
    
    def _check_database_config(self) -> Dict[str, Any]:
        """检查数据库配置"""
        try:
            from app.database import engine
            
            # 尝试连接数据库
            with engine.connect() as conn:
                result = conn.execute("SELECT 1")
                result.fetchone()
            
            return {
                "status": "pass",
                "message": "数据库连接正常",
                "details": {
                    "database_url": str(engine.url).replace(engine.url.password or "", "****"),
                    "connection_successful": True
                }
            }
            
        except Exception as e:
            return {
                "status": "fail",
                "message": f"数据库连接失败: {str(e)}",
                "details": {
                    "connection_successful": False,
                    "error": str(e)
                }
            }
    
    def _check_certificate_files(self) -> Dict[str, Any]:
        """检查证书文件"""
        try:
            config = get_wechat_pay_config()
            cert_path = config.get("cert_path", "")
            key_path = config.get("key_path", "")
            
            cert_exists = os.path.exists(cert_path) if cert_path else False
            key_exists = os.path.exists(key_path) if key_path else False
            
            issues = []
            if not cert_exists:
                issues.append(f"证书文件不存在: {cert_path}")
            if not key_exists:
                issues.append(f"私钥文件不存在: {key_path}")
            
            if issues:
                return {
                    "status": "warning",
                    "message": "; ".join(issues),
                    "details": {
                        "cert_path": cert_path,
                        "key_path": key_path,
                        "cert_exists": cert_exists,
                        "key_exists": key_exists
                    }
                }
            
            return {
                "status": "pass",
                "message": "证书文件检查通过",
                "details": {
                    "cert_path": cert_path,
                    "key_path": key_path,
                    "cert_exists": cert_exists,
                    "key_exists": key_exists
                }
            }
            
        except Exception as e:
            return {
                "status": "fail",
                "message": f"证书文件检查失败: {str(e)}",
                "details": {}
            }
    
    def _check_network_connectivity(self) -> Dict[str, Any]:
        """检查网络连通性"""
        try:
            config = get_wechat_pay_config()
            api_base_url = config.get("api_base_url", "https://api.mch.weixin.qq.com")
            
            # 测试网络连通性
            try:
                response = requests.get(f"{api_base_url}/v3/certificates", timeout=10)
                network_ok = True
                status_code = response.status_code
                response_time = response.elapsed.total_seconds()
            except requests.RequestException as e:
                network_ok = False
                status_code = None
                response_time = None
                error_message = str(e)
            
            if network_ok:
                return {
                    "status": "pass",
                    "message": "网络连通性正常",
                    "details": {
                        "api_base_url": api_base_url,
                        "status_code": status_code,
                        "response_time": response_time,
                        "network_ok": True
                    }
                }
            else:
                return {
                    "status": "warning",
                    "message": f"网络连通性异常: {error_message}",
                    "details": {
                        "api_base_url": api_base_url,
                        "network_ok": False,
                        "error": error_message
                    }
                }
                
        except Exception as e:
            return {
                "status": "fail",
                "message": f"网络连通性检查失败: {str(e)}",
                "details": {}
            }
    
    def _check_environment_variables(self) -> Dict[str, Any]:
        """检查环境变量"""
        try:
            required_env_vars = [
                "WECHAT_PAY_APPID",
                "WECHAT_PAY_MCHID",
                "WECHAT_PAY_API_V3_KEY"
            ]
            
            missing_vars = []
            present_vars = []
            
            for var in required_env_vars:
                if os.environ.get(var):
                    present_vars.append(var)
                else:
                    missing_vars.append(var)
            
            # 检查.env文件
            env_file_exists = os.path.exists(".env")
            
            if missing_vars:
                return {
                    "status": "warning",
                    "message": f"缺少环境变量: {', '.join(missing_vars)}",
                    "details": {
                        "missing_vars": missing_vars,
                        "present_vars": present_vars,
                        "env_file_exists": env_file_exists
                    }
                }
            
            return {
                "status": "pass",
                "message": "环境变量检查通过",
                "details": {
                    "present_vars": present_vars,
                    "env_file_exists": env_file_exists,
                    "total_vars": len(present_vars)
                }
            }
            
        except Exception as e:
            return {
                "status": "fail",
                "message": f"环境变量检查失败: {str(e)}",
                "details": {}
            }
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        try:
            config = get_wechat_pay_config()
            
            return {
                "payment_config": {
                    "appid": config["appid"][:8] + "****" if config["appid"] else "未配置",
                    "mchid": config["mchid"][:4] + "****" if config["mchid"] else "未配置",
                    "sandbox": config["sandbox"],
                    "notify_url": config["notify_url"],
                    "order_prefix": config["order_prefix"],
                    "order_expire_minutes": config["order_expire_minutes"]
                },
                "environment": {
                    "payment_env": os.environ.get("PAYMENT_ENV", "未设置"),
                    "app_mode": os.environ.get("APP_MODE", "development"),
                    "debug": os.environ.get("DEBUG", "false").lower() == "true"
                },
                "files": {
                    "env_file_exists": os.path.exists(".env"),
                    "cert_file_exists": os.path.exists(config.get("cert_path", "")),
                    "key_file_exists": os.path.exists(config.get("key_path", ""))
                }
            }
            
        except Exception as e:
            logger.error(f"获取配置摘要失败: {e}")
            return {
                "error": str(e)
            }


# 创建全局实例
config_health_check_service = ConfigHealthCheckService()
