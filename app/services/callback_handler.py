"""
支付回调处理服务
"""
import json
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.database import get_db, SessionLocal
from app.models.payment import Order, PaymentLog, OrderStatus
from app.services.payment_service import payment_service
from app.services.wechat_pay_service import wechat_pay_service

logger = logging.getLogger(__name__)


class CallbackHandler:
    """支付回调处理器"""
    
    def __init__(self):
        self.max_retry_times = 3
        self.retry_intervals = [1, 5, 15]  # 重试间隔（秒）
    
    async def handle_payment_callback(
        self, 
        headers: Dict[str, str], 
        body: str,
        request_ip: Optional[str] = None
    ) -> Dict[str, str]:
        """
        处理支付回调
        
        Args:
            headers: 请求头
            body: 请求体
            request_ip: 请求IP
            
        Returns:
            Dict[str, str]: 响应结果
        """
        db = SessionLocal()
        try:
            # 记录回调接收日志
            self._log_callback_received(db, headers, body, request_ip)
            
            # 验证签名
            if not self._verify_signature(headers, body):
                logger.error("支付回调签名验证失败")
                return {"code": "FAIL", "message": "签名验证失败"}
            
            # 解析回调数据
            callback_data = self._parse_callback_data(body)
            if not callback_data:
                logger.error("支付回调数据解析失败")
                return {"code": "FAIL", "message": "数据解析失败"}
            
            # 获取订单号
            order_id = callback_data.get("out_trade_no")
            if not order_id:
                logger.error("支付回调缺少订单号")
                return {"code": "FAIL", "message": "缺少订单号"}
            
            # 检查订单是否存在
            order = self._get_order(db, order_id)
            if not order:
                logger.error(f"支付回调订单不存在: {order_id}")
                return {"code": "FAIL", "message": "订单不存在"}
            
            # 检查是否重复回调
            if self._is_duplicate_callback(db, order_id, callback_data):
                logger.info(f"重复的支付回调: {order_id}")
                return {"code": "SUCCESS", "message": "重复回调"}
            
            # 处理回调
            success = await self._process_callback_with_retry(db, order, callback_data)
            
            if success:
                logger.info(f"支付回调处理成功: {order_id}")
                return {"code": "SUCCESS", "message": "成功"}
            else:
                logger.error(f"支付回调处理失败: {order_id}")
                return {"code": "FAIL", "message": "处理失败"}
                
        except Exception as e:
            logger.exception("支付回调处理异常")
            return {"code": "FAIL", "message": "服务器错误"}
        finally:
            db.close()
    
    def _verify_signature(self, headers: Dict[str, str], body: str) -> bool:
        """验证回调签名"""
        try:
            if not wechat_pay_service:
                logger.error("微信支付服务未初始化")
                return False
            
            return wechat_pay_service.verify_callback_signature(headers, body)
        except Exception as e:
            logger.error(f"签名验证异常: {e}")
            return False
    
    def _parse_callback_data(self, body: str) -> Optional[Dict[str, Any]]:
        """解析回调数据"""
        try:
            if not wechat_pay_service:
                logger.error("微信支付服务未初始化")
                return None
            
            return wechat_pay_service.parse_callback_data(body)
        except Exception as e:
            logger.error(f"解析回调数据异常: {e}")
            return None
    
    def _get_order(self, db: Session, order_id: str) -> Optional[Order]:
        """获取订单"""
        try:
            return db.query(Order).filter(Order.id == order_id).first()
        except Exception as e:
            logger.error(f"查询订单异常: {order_id}, {e}")
            return None
    
    def _is_duplicate_callback(
        self, 
        db: Session, 
        order_id: str, 
        callback_data: Dict[str, Any]
    ) -> bool:
        """检查是否重复回调"""
        try:
            transaction_id = callback_data.get("transaction_id")
            if not transaction_id:
                return False
            
            # 检查是否已有相同交易号的成功回调记录
            existing_log = db.query(PaymentLog).filter(
                and_(
                    PaymentLog.order_id == order_id,
                    PaymentLog.action == "payment_callback",
                    PaymentLog.status == "success",
                    PaymentLog.response_data.contains(transaction_id)
                )
            ).first()
            
            return existing_log is not None
            
        except Exception as e:
            logger.error(f"检查重复回调异常: {e}")
            return False
    
    async def _process_callback_with_retry(
        self, 
        db: Session, 
        order: Order, 
        callback_data: Dict[str, Any]
    ) -> bool:
        """带重试的回调处理"""
        for attempt in range(self.max_retry_times):
            try:
                # 处理支付回调
                success = payment_service.process_payment_callback(db, callback_data)
                
                if success:
                    # 记录成功日志
                    self._log_callback_processed(
                        db, order.id, order.user_id, "success", 
                        callback_data, attempt + 1
                    )
                    return True
                else:
                    # 记录失败日志
                    self._log_callback_processed(
                        db, order.id, order.user_id, "failed", 
                        callback_data, attempt + 1, "处理失败"
                    )
                    
                    # 如果不是最后一次尝试，等待后重试
                    if attempt < self.max_retry_times - 1:
                        await asyncio.sleep(self.retry_intervals[attempt])
                        logger.info(f"支付回调处理重试: {order.id}, 第{attempt + 2}次")
                    
            except Exception as e:
                error_msg = str(e)
                logger.error(f"支付回调处理异常: {order.id}, 第{attempt + 1}次, {error_msg}")
                
                # 记录异常日志
                self._log_callback_processed(
                    db, order.id, order.user_id, "error", 
                    callback_data, attempt + 1, error_msg
                )
                
                # 如果不是最后一次尝试，等待后重试
                if attempt < self.max_retry_times - 1:
                    await asyncio.sleep(self.retry_intervals[attempt])
                    logger.info(f"支付回调处理重试: {order.id}, 第{attempt + 2}次")
        
        # 所有重试都失败
        logger.error(f"支付回调处理最终失败: {order.id}")
        return False
    
    def _log_callback_received(
        self, 
        db: Session, 
        headers: Dict[str, str], 
        body: str,
        request_ip: Optional[str] = None
    ):
        """记录回调接收日志"""
        try:
            log = PaymentLog(
                action="callback_received",
                status="received",
                request_data={
                    "headers": dict(headers),
                    "body_length": len(body),
                    "request_ip": request_ip,
                    "timestamp": datetime.now().isoformat()
                },
                ip_address=request_ip
            )
            db.add(log)
            db.commit()
        except Exception as e:
            logger.error(f"记录回调接收日志失败: {e}")
    
    def _log_callback_processed(
        self, 
        db: Session, 
        order_id: str, 
        user_id: int, 
        status: str,
        callback_data: Dict[str, Any],
        attempt: int,
        error_message: Optional[str] = None
    ):
        """记录回调处理日志"""
        try:
            log = PaymentLog(
                order_id=order_id,
                user_id=user_id,
                action="callback_processed",
                status=status,
                request_data=callback_data,
                response_data={
                    "attempt": attempt,
                    "timestamp": datetime.now().isoformat()
                },
                error_message=error_message
            )
            db.add(log)
            db.commit()
        except Exception as e:
            logger.error(f"记录回调处理日志失败: {e}")
    
    async def handle_timeout_orders(self):
        """处理超时订单"""
        db = SessionLocal()
        try:
            # 查找超时的待支付订单
            timeout_orders = db.query(Order).filter(
                and_(
                    Order.status == OrderStatus.PENDING,
                    Order.expired_at < datetime.now()
                )
            ).all()
            
            logger.info(f"发现 {len(timeout_orders)} 个超时订单")
            
            for order in timeout_orders:
                try:
                    # 更新订单状态为过期
                    order.status = OrderStatus.EXPIRED
                    order.cancelled_at = datetime.now()
                    order.cancel_reason = "订单超时自动取消"
                    
                    # 如果有微信预支付ID，尝试关闭微信订单
                    if order.wx_prepay_id and wechat_pay_service:
                        try:
                            wechat_pay_service.close_order(order.id)
                        except Exception as e:
                            logger.warning(f"关闭超时微信订单失败: {order.id}, {e}")
                    
                    # 记录日志
                    log = PaymentLog(
                        order_id=order.id,
                        user_id=order.user_id,
                        action="order_timeout",
                        status="expired",
                        request_data={
                            "expired_at": order.expired_at.isoformat(),
                            "processed_at": datetime.now().isoformat()
                        }
                    )
                    db.add(log)
                    
                    logger.info(f"订单超时处理完成: {order.id}")
                    
                except Exception as e:
                    logger.error(f"处理超时订单异常: {order.id}, {e}")
            
            db.commit()
            
        except Exception as e:
            db.rollback()
            logger.error(f"处理超时订单异常: {e}")
        finally:
            db.close()


# 创建全局实例
callback_handler = CallbackHandler()
