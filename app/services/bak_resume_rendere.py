import os
import base64
from jinja2 import Environment, FileSystemLoader, select_autoescape
from app.schemas.resume import ResumeData

from env import RESUME_SERVER_PORT

# RESUME_SERVER_PORT = os.getenv("RESUME_SERVER_PORT")

class ResumeRenderer:
    def __init__(self):
        # 获取模板目录的绝对路径
        template_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "app", "templates")
        self.env = Environment(
            loader=FileSystemLoader(template_dir),
            autoescape=select_autoescape(['html', 'xml'])
        )
        
    # def process_photo(self, resume_data: ResumeData):
    #     """处理照片数据，如果存在Base64编码的照片"""
    #     if resume_data.basicInfo.photoUrl and resume_data.basicInfo.photoUrl.startswith("data:image"):
    #         # 提取实际的base64数据
    #         try:
    #             _, data = resume_data.basicInfo.photoUrl.split(',', 1)
    #             # 在这里我们只是保留处理后的base64数据用于模板渲染
    #             resume_data.basicInfo.photoUrl = data
    #         except Exception as e:
    #             print(f"处理照片数据时出错: {e}")
    #     return resume_data
    
    def render_template(self, 
                        resume_data: ResumeData, 
                        template_id_from_client: str = "templateA02.html", 
                        config_from_client: dict = None) -> str:
        """渲染简历模板
        
        Args:
            resume_data: 简历数据对象
            template_name: 模板文件名
            theme_config: 主题配置，如颜色、字体大小等
        """
        print(f'TEMPLATE_ID: {template_id_from_client}')
        print(f'CONFIG: {config_from_client}')
        print(f'resume_data: {resume_data}')
        
        # 处理照片
        # resume_data = self.process_photo(resume_data)
        
        # 获取模板
        if not template_id_from_client.endswith('.html'):
            template_id_from_client = f'{template_id_from_client}.html'
        template = self.env.get_template(template_id_from_client)
        
        
        # TODO 修改默认主题配置
        # 默认主题配置
        default_theme = {
            "theme_color": "#44546b",
            "base_font_size": 11,
            "max_font_size": 12,
            "spacing": 1.2,
        }
        
        # 合并配置
        if 'themeColor' in config_from_client:
            default_theme['theme_color'] = config_from_client['themeColor']
        if 'fontSize' in config_from_client:
            default_theme['base_font_size'] = config_from_client['fontSize']
        if 'spacing' in config_from_client:
            default_theme['spacing'] = config_from_client['spacing']
        
        # 设置隐藏标志
        hide_flags = {
            "hide_basic_info": not (resume_data.basicInfo.name or resume_data.basicInfo.phone or 
                                   resume_data.basicInfo.email or resume_data.basicInfo.gender),
            "hide_job_intention": not (hasattr(resume_data, 'jobIntention') and resume_data.jobIntention),
            "hide_education": not (hasattr(resume_data, 'education') and resume_data.education and len(resume_data.education) > 0),
            "hide_school_experience": not (hasattr(resume_data, 'school') and resume_data.school and len(resume_data.school) > 0),
            "hide_work": not (hasattr(resume_data, 'work') and resume_data.work and len(resume_data.work) > 0),
            "hide_project": not (hasattr(resume_data, 'project') and resume_data.project and len(resume_data.project) > 0),
            "hide_skills": not (hasattr(resume_data, 'skills') and resume_data.skills and len(resume_data.skills) > 0),
            "hide_awards": not (hasattr(resume_data, 'awards') and resume_data.awards and len(resume_data.awards) > 0),
            "hide_interests": not (hasattr(resume_data, 'interests') and resume_data.interests and len(resume_data.interests) > 0),
            "hide_evaluation": not (hasattr(resume_data, 'evaluation') and resume_data.evaluation),
            "hide_custom1": not (hasattr(resume_data, 'custom') and resume_data.custom and 
                                hasattr(resume_data.custom, 'custom1') and resume_data.custom.custom1),
            "hide_custom2": not (hasattr(resume_data, 'custom') and resume_data.custom and 
                                hasattr(resume_data.custom, 'custom2') and resume_data.custom.custom2),
            "hide_custom3": not (hasattr(resume_data, 'custom') and resume_data.custom and 
                                hasattr(resume_data.custom, 'custom3') and resume_data.custom.custom3)
        }
        
        render_config = {
            "base_url": f"http://localhost:{RESUME_SERVER_PORT}/static"
        }
        
        # 渲染模板
        render_context = {
            "resume": resume_data,
            **default_theme,
            **hide_flags,
            **render_config
        }
        
        html_content = template.render(**render_context)
        
        return html_content 