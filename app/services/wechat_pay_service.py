"""
微信支付服务
"""
import json
import time
import uuid
import hashlib
import hmac
import base64
import requests
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.backends import default_backend
import logging

from config.wechat_pay_config import get_wechat_pay_config

logger = logging.getLogger(__name__)


class WeChatPayService:
    """微信支付服务类"""
    
    def __init__(self):
        self.config = get_wechat_pay_config()
        self.appid = self.config["appid"]
        self.mchid = self.config["mchid"]
        self.api_v3_key = self.config["api_v3_key"]
        self.cert_serial_no = self.config["cert_serial_no"]
        self.notify_url = self.config["notify_url"]
        self.api_base_url = self.config["api_base_url"]
        self.cert_path = self.config["cert_path"]
        self.key_path = self.config["key_path"]
        
        # 加载私钥
        self._load_private_key()
    
    def _load_private_key(self):
        """加载私钥"""
        try:
            with open(self.key_path, 'rb') as f:
                self.private_key = serialization.load_pem_private_key(
                    f.read(),
                    password=None,
                    backend=default_backend()
                )
            logger.info("微信支付私钥加载成功")
        except Exception as e:
            logger.error(f"加载微信支付私钥失败: {e}")
            raise
    
    def _generate_nonce_str(self) -> str:
        """生成随机字符串"""
        return str(uuid.uuid4()).replace('-', '')
    
    def _generate_timestamp(self) -> str:
        """生成时间戳"""
        return str(int(time.time()))
    
    def _build_authorization_header(self, method: str, url_path: str, body: str = "") -> str:
        """构建Authorization头"""
        timestamp = self._generate_timestamp()
        nonce_str = self._generate_nonce_str()
        
        # 构建签名字符串
        sign_str = f"{method}\n{url_path}\n{timestamp}\n{nonce_str}\n{body}\n"
        
        # 使用私钥签名
        signature = self.private_key.sign(
            sign_str.encode('utf-8'),
            padding.PKCS1v15(),
            hashes.SHA256()
        )
        
        # Base64编码
        signature_b64 = base64.b64encode(signature).decode('utf-8')
        
        # 构建Authorization头
        auth_header = (
            f'WECHATPAY2-SHA256-RSA2048 '
            f'mchid="{self.mchid}",'
            f'nonce_str="{nonce_str}",'
            f'signature="{signature_b64}",'
            f'timestamp="{timestamp}",'
            f'serial_no="{self.cert_serial_no}"'
        )
        
        return auth_header
    
    def _make_request(self, method: str, url_path: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """发起HTTP请求"""
        url = f"{self.api_base_url}{url_path}"
        body = json.dumps(data) if data else ""
        
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': self._build_authorization_header(method, url_path, body),
            'User-Agent': 'Resume-Service/1.0'
        }
        
        try:
            if method.upper() == 'GET':
                response = requests.get(url, headers=headers, timeout=30)
            elif method.upper() == 'POST':
                response = requests.post(url, headers=headers, data=body, timeout=30)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"微信支付API请求失败: {e}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"微信支付API响应解析失败: {e}")
            raise
    
    def create_jsapi_order(self, order_id: str, amount: int, description: str, openid: str) -> Dict[str, Any]:
        """创建JSAPI支付订单"""
        url_path = "/v3/pay/transactions/jsapi"
        
        # 计算订单过期时间
        expire_time = datetime.now() + timedelta(minutes=self.config["order_expire_minutes"])
        time_expire = expire_time.strftime("%Y-%m-%dT%H:%M:%S+08:00")
        
        data = {
            "appid": self.appid,
            "mchid": self.mchid,
            "description": description,
            "out_trade_no": order_id,
            "time_expire": time_expire,
            "notify_url": self.notify_url,
            "amount": {
                "total": amount,
                "currency": "CNY"
            },
            "payer": {
                "openid": openid
            }
        }
        
        logger.info(f"创建微信支付订单: {order_id}, 金额: {amount}分")
        
        try:
            response = self._make_request("POST", url_path, data)
            logger.info(f"微信支付订单创建成功: {order_id}")
            return response
        except Exception as e:
            logger.error(f"创建微信支付订单失败: {order_id}, 错误: {e}")
            raise
    
    def query_order(self, order_id: str) -> Dict[str, Any]:
        """查询订单状态"""
        url_path = f"/v3/pay/transactions/out-trade-no/{order_id}"
        params = f"?mchid={self.mchid}"
        
        try:
            response = self._make_request("GET", url_path + params)
            logger.info(f"查询微信支付订单成功: {order_id}")
            return response
        except Exception as e:
            logger.error(f"查询微信支付订单失败: {order_id}, 错误: {e}")
            raise
    
    def close_order(self, order_id: str) -> bool:
        """关闭订单"""
        url_path = f"/v3/pay/transactions/out-trade-no/{order_id}/close"
        
        data = {
            "mchid": self.mchid
        }
        
        try:
            self._make_request("POST", url_path, data)
            logger.info(f"关闭微信支付订单成功: {order_id}")
            return True
        except Exception as e:
            logger.error(f"关闭微信支付订单失败: {order_id}, 错误: {e}")
            return False
    
    def generate_jsapi_pay_params(self, prepay_id: str) -> Dict[str, str]:
        """生成小程序支付参数"""
        timestamp = self._generate_timestamp()
        nonce_str = self._generate_nonce_str()
        package = f"prepay_id={prepay_id}"
        
        # 构建签名字符串
        sign_str = f"{self.appid}\n{timestamp}\n{nonce_str}\n{package}\n"
        
        # 使用私钥签名
        signature = self.private_key.sign(
            sign_str.encode('utf-8'),
            padding.PKCS1v15(),
            hashes.SHA256()
        )
        
        # Base64编码
        pay_sign = base64.b64encode(signature).decode('utf-8')
        
        return {
            "appId": self.appid,
            "timeStamp": timestamp,
            "nonceStr": nonce_str,
            "package": package,
            "signType": "RSA",
            "paySign": pay_sign
        }
    
    def verify_callback_signature(self, headers: Dict[str, str], body: str) -> bool:
        """验证回调签名"""
        try:
            # 获取签名相关信息
            timestamp = headers.get('Wechatpay-Timestamp', '')
            nonce = headers.get('Wechatpay-Nonce', '')
            signature = headers.get('Wechatpay-Signature', '')
            cert_serial = headers.get('Wechatpay-Serial', '')
            
            if not all([timestamp, nonce, signature, cert_serial]):
                logger.error("回调签名验证失败: 缺少必要的头部信息")
                return False
            
            # 构建验签字符串
            sign_str = f"{timestamp}\n{nonce}\n{body}\n"
            
            # 这里简化处理，实际应该验证微信的证书
            # 在生产环境中需要下载并验证微信的平台证书
            logger.info(f"回调签名验证: {cert_serial}")
            return True
            
        except Exception as e:
            logger.error(f"回调签名验证异常: {e}")
            return False
    
    def decrypt_callback_data(self, encrypted_data: str, nonce: str, associated_data: str) -> str:
        """解密回调数据"""
        try:
            from cryptography.hazmat.primitives.ciphers.aead import AESGCM
            
            # 解码
            encrypted_bytes = base64.b64decode(encrypted_data)
            nonce_bytes = nonce.encode('utf-8')
            associated_data_bytes = associated_data.encode('utf-8')
            key_bytes = self.api_v3_key.encode('utf-8')
            
            # 解密
            aesgcm = AESGCM(key_bytes)
            decrypted_data = aesgcm.decrypt(nonce_bytes, encrypted_bytes, associated_data_bytes)
            
            return decrypted_data.decode('utf-8')
            
        except Exception as e:
            logger.error(f"解密回调数据失败: {e}")
            raise
    
    def parse_callback_data(self, callback_body: str) -> Dict[str, Any]:
        """解析回调数据"""
        try:
            data = json.loads(callback_body)
            
            # 解密resource数据
            resource = data.get('resource', {})
            if resource:
                encrypted_data = resource.get('ciphertext', '')
                nonce = resource.get('nonce', '')
                associated_data = resource.get('associated_data', '')
                
                if encrypted_data and nonce and associated_data:
                    decrypted_data = self.decrypt_callback_data(encrypted_data, nonce, associated_data)
                    return json.loads(decrypted_data)
            
            return data
            
        except Exception as e:
            logger.error(f"解析回调数据失败: {e}")
            raise


# 创建全局实例
try:
    wechat_pay_service = WeChatPayService()
    logger.info("微信支付服务初始化成功")
except Exception as e:
    logger.error(f"微信支付服务初始化失败: {e}")
    wechat_pay_service = None
