"""
用户操作记录服务
基于前端主动上报的理念，管理用户操作记录和权益使用
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, and_

from app.models import User, UserAction
from app.services.membership_service import membership_service

logger = logging.getLogger(__name__)


class UserActionService:
    """用户操作记录服务"""
    
    # 定义会员权益操作类型
    MEMBER_ACTION_TYPES = {
        "download_pdf": "resume_export",
        "export_jpeg": "resume_export", 
        "generate_idphoto": "idphoto_generate",
        "use_premium_template": "premium_templates",
        "watermark_free_export": "watermark_free"
    }
    
    def validate_action_permission(
        self, 
        db: Session, 
        user_id: int, 
        action_type: str,
        check_quota: bool = False
    ) -> Dict[str, Any]:
        """
        验证用户操作权限
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            action_type: 操作类型
            check_quota: 是否检查配额
        
        Returns:
            权限验证结果
        """
        try:
            # 检查是否为会员权益操作
            feature = self.MEMBER_ACTION_TYPES.get(action_type)
            if not feature:
                return {
                    "has_permission": True,
                    "is_member_action": False,
                    "message": "非会员权益操作，允许记录"
                }
            
            # 检查会员权限
            permission_result = membership_service.check_feature_permission(
                db=db,
                user_id=user_id,
                feature=feature,
                check_usage=check_quota
            )
            
            return {
                "has_permission": permission_result["has_permission"],
                "is_member_action": True,
                "feature": feature,
                "reason": permission_result.get("reason", ""),
                "usage_info": permission_result.get("usage_info", {}),
                "is_member": permission_result.get("is_member", False)
            }
            
        except Exception as e:
            logger.error(f"验证操作权限异常: {user_id}, {action_type}, {e}")
            return {
                "has_permission": False,
                "is_member_action": True,
                "message": "权限验证失败"
            }
    
    def record_user_action(
        self,
        db: Session,
        user_id: int,
        action_type: str,
        action_content: Optional[Dict[str, Any]] = None,
        feature_name: Optional[str] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        file_size: Optional[int] = None,
        file_format: Optional[str] = None,
        operation_status: str = "completed",
        error_message: Optional[str] = None,
        consumed_quota: int = 1,
        client_info: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        template_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        记录用户操作
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            action_type: 操作类型
            其他参数: 操作详细信息
        
        Returns:
            记录结果
        """
        try:
            # 检查是否为会员权益操作
            is_member_action = action_type in self.MEMBER_ACTION_TYPES
            
            # 如果是会员操作，自动设置feature_name
            if is_member_action and not feature_name:
                feature_name = self.MEMBER_ACTION_TYPES[action_type]
            
            # 创建用户行为记录
            user_action = UserAction(
                user_id=user_id,
                action_type=action_type,
                action_content=action_content,
                feature_name=feature_name,
                resource_type=resource_type,
                resource_id=resource_id,
                file_size=file_size,
                file_format=file_format,
                operation_status=operation_status,
                error_message=error_message,
                is_member_action=is_member_action,
                consumed_quota=consumed_quota,
                client_info=client_info,
                ip_address=ip_address,
                template_id=template_id
            )
            
            db.add(user_action)
            db.commit()
            db.refresh(user_action)
            
            logger.info(f"记录用户操作成功: 用户{user_id}, 操作{action_type}, ID{user_action.id}")
            
            return {
                "success": True,
                "action_id": user_action.id,
                "is_member_action": is_member_action,
                "consumed_quota": consumed_quota,
                "message": "操作记录成功"
            }
            
        except Exception as e:
            db.rollback()
            logger.error(f"记录用户操作失败: {user_id}, {action_type}, {e}")
            return {
                "success": False,
                "message": f"记录操作失败: {str(e)}"
            }
    
    def get_user_action_stats(
        self,
        db: Session,
        user_id: int,
        days: int = 30
    ) -> Dict[str, Any]:
        """
        获取用户操作统计
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            days: 统计天数
        
        Returns:
            统计结果
        """
        try:
            # 计算统计开始时间
            start_date = datetime.now() - timedelta(days=days)
            
            # 统计总操作次数
            total_actions = db.query(func.count(UserAction.id)).filter(
                and_(
                    UserAction.user_id == user_id,
                    UserAction.created_at >= start_date
                )
            ).scalar() or 0
            
            # 统计会员权益操作次数
            member_actions = db.query(func.count(UserAction.id)).filter(
                and_(
                    UserAction.user_id == user_id,
                    UserAction.is_member_action == True,
                    UserAction.created_at >= start_date
                )
            ).scalar() or 0
            
            # 统计各功能使用次数
            feature_stats = db.query(
                UserAction.feature_name,
                func.count(UserAction.id).label('count'),
                func.sum(UserAction.consumed_quota).label('total_quota')
            ).filter(
                and_(
                    UserAction.user_id == user_id,
                    UserAction.feature_name.isnot(None),
                    UserAction.created_at >= start_date
                )
            ).group_by(UserAction.feature_name).all()
            
            # 统计操作状态分布
            status_stats = db.query(
                UserAction.operation_status,
                func.count(UserAction.id).label('count')
            ).filter(
                and_(
                    UserAction.user_id == user_id,
                    UserAction.created_at >= start_date
                )
            ).group_by(UserAction.operation_status).all()
            
            return {
                "period_days": days,
                "start_date": start_date.date(),
                "end_date": datetime.now().date(),
                "total_actions": total_actions,
                "member_actions": member_actions,
                "feature_stats": {
                    stat.feature_name: {
                        "count": stat.count,
                        "total_quota": stat.total_quota or 0
                    } for stat in feature_stats
                },
                "status_stats": {
                    stat.operation_status: stat.count for stat in status_stats
                }
            }
            
        except Exception as e:
            logger.error(f"获取用户操作统计失败: {user_id}, {e}")
            return {
                "period_days": days,
                "start_date": (datetime.now() - timedelta(days=days)).date(),
                "end_date": datetime.now().date(),
                "total_actions": 0,
                "member_actions": 0,
                "feature_stats": {},
                "status_stats": {}
            }


# 创建服务实例
user_action_service = UserActionService()
