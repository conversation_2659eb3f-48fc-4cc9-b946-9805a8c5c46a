import httpx
import logging
from typing import Optional, Dict, Any, Union
import json
import requests

from config.fastapi_config import settings

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 从环境变量获取PDF服务URL，如果没有设置则使用默认值
# PDF_SERVICE_URL = os.environ.get("PDF_SERVICE_URL")
# print(f'--------pdf service url: {os.environ}')

class PDFService:
    """
    调用Node.js PDF转换服务的客户端
    """

    def __init__(self, pdf_base_url: str = settings.PDF_SERVICE_URL):
        """
        初始化PDF服务客户端
        
        Args:
            base_url: PDF转换服务的基础URL
        """
        self.pdf_convert_url = f"{pdf_base_url}/convertpdf"
        self.jpeg_convert_url = f"{pdf_base_url}/convertjpeg"
        self.pdf_health_url = f"{pdf_base_url}/health"
        logger.info(f"PDF服务初始化，PFD SERVER URL: {pdf_base_url}")
        
    async def check_health(self) -> Dict[str, Any]:
        """
        检查PDF服务的健康状态
        
        Returns:
            字典包含健康状态信息
        """
        try:
            with httpx.Client() as client:
                # print(f'health_url: {self.health_url}')
                response = client.get(self.pdf_health_url, timeout=5.0)
                response.raise_for_status()
                return response.json()
        except Exception as e:
            logger.error(f"检查PDF服务健康状态失败: {str(e)}")
            return {"status": "error", "message": str(e)}
            
    async def html_to_pdf(self, 
                         html_content: str, 
                         options: Optional[Dict[str, Any]] = None) -> Union[bytes, Dict[str, str]]:
        """
        将HTML转换为PDF
        
        Args:
            html_content: HTML内容
            options: PDF转换选项，例如页面大小、页边距等
            
        Returns:
            PDF二进制数据或错误信息
        """
        if options is None:
            options = {}
            
        try:
            # 准备请求数据
            data = {
                "html": html_content,
                "options": options
            }
            
            logger.info(f"正在发送HTML到PDF转换请求，HTML大小: {len(html_content)} 字节")
            
            # 发送请求到PDF转换服务
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.pdf_convert_url,
                    json=data,
                    timeout=60.0,  # 较长的超时时间以允许PDF生成
                    headers={"Content-Type": "application/json"}
                )
                
                # 检查响应
                if response.status_code == 200:
                    logger.info("PDF生成成功")
                    return response.content
                else:
                    logger.error(f"PDF服务返回错误: {response.status_code}, {response.text}")
                    try:
                        error_data = response.json()
                        return {"error": error_data.get("error", "未知错误"), "message": error_data.get("message", "")}
                    except Exception:
                        return {"error": "PDF转换失败", "message": response.text}
                        
        except Exception as e:
            logger.error(f"调用PDF服务时出错: {str(e)}")
            return {"error": "PDF服务连接失败", "message": str(e)} 

    async def html_to_jpeg(self, 
                           html_content: str, 
                           options: Optional[Dict[str, Any]] = None) -> Union[bytes, Dict[str, str]]:
        """
        将HTML转换为JPEG图片
        
        Args:
            html_content: HTML内容
            options: JPEG转换选项，例如质量、是否全页截图等
            
        Returns:
            JPEG二进制数据或错误信息
        """
        if options is None:
            options = {}
            
        try:
            # 准备请求数据
            data = {
                "html": html_content,
                "options": options
            }
            
            # 构建JPEG转换URL - 修改为convertjpeg端点
            jpeg_convert_url = self.jpeg_convert_url
            
            logger.info(f"正在发送HTML到JPEG转换请求，HTML大小: {len(html_content)} 字节")
            
            # 发送请求到JPEG转换服务
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    jpeg_convert_url,
                    json=data,
                    timeout=60.0,  # 较长的超时时间以允许图片生成
                    headers={"Content-Type": "application/json"}
                )
                
                # 检查响应
                if response.status_code == 200:
                    logger.info("JPEG生成成功")
                    return response.content
                else:
                    logger.error(f"JPEG服务返回错误: {response.status_code}, {response.text}")
                    try:
                        error_data = response.json()
                        return {"error": error_data.get("error", "未知错误"), "message": error_data.get("message", "")}
                    except Exception:
                        return {"error": "JPEG转换失败", "message": response.text}
                        
        except Exception as e:
            logger.error(f"调用JPEG服务时出错: {str(e)}")
            return {"error": "JPEG服务连接失败", "message": str(e)} 