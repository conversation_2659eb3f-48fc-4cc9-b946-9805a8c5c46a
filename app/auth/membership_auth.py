"""
会员权限认证模块
包含装饰器和依赖注入两种权限检查方式
"""
import functools
import logging
from typing import Callable, Optional, Dict, Any
from fastapi import HTTPException, status, Depends
from sqlalchemy.orm import Session

from app.database import get_db
from app.models import User
from app.auth.base import get_current_user
from app.services.membership_service import membership_service

logger = logging.getLogger(__name__)


class MembershipRequired:
    """会员权限检查装饰器"""
    
    def __init__(
        self, 
        feature: str,
        check_usage: bool = True,
        auto_record: bool = True,
        error_message: Optional[str] = None
    ):
        """
        初始化会员权限检查装饰器
        
        Args:
            feature: 功能名称
            check_usage: 是否检查使用次数
            auto_record: 是否自动记录使用
            error_message: 自定义错误消息
        """
        self.feature = feature
        self.check_usage = check_usage
        self.auto_record = auto_record
        self.error_message = error_message or f"使用{feature}功能需要会员权限"
    
    def __call__(self, func: Callable) -> Callable:
        """装饰器实现"""
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取依赖注入的参数
            current_user = None
            db = None
            
            for key, value in kwargs.items():
                if isinstance(value, User):
                    current_user = value
                elif hasattr(value, 'query'):  # Session对象
                    db = value
            
            if not current_user or not db:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="权限检查失败：缺少必要参数"
                )
            
            # 检查功能权限
            permission_result = membership_service.check_feature_permission(
                db=db,
                user_id=current_user.id,
                feature=self.feature,
                check_usage=self.check_usage
            )
            
            if not permission_result["has_permission"]:
                # 构建错误响应
                error_detail = {
                    "error": "permission_denied",
                    "message": permission_result["reason"],
                    "feature": self.feature,
                    "is_member": permission_result["is_member"],
                    "upgrade_required": permission_result.get("upgrade_required", False),
                    "usage_info": permission_result.get("usage_info", {})
                }
                
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=error_detail
                )
            
            # 执行原函数
            result = await func(*args, **kwargs)

            # 注意：不再自动记录使用，改为前端主动上报
            # 前端需要在用户真正完成操作后调用 /user/action/report 接口
            if self.auto_record:
                logger.info(f"功能 {self.feature} 执行成功，等待前端主动上报使用记录")

            return result
        
        return wrapper


def require_membership(
    feature: str,
    check_usage: bool = True,
    auto_record: bool = True,
    error_message: Optional[str] = None
):
    """
    会员权限检查装饰器工厂函数
    
    Args:
        feature: 功能名称
        check_usage: 是否检查使用次数
        auto_record: 是否自动记录使用
        error_message: 自定义错误消息
    
    Returns:
        装饰器函数
    """
    return MembershipRequired(
        feature=feature,
        check_usage=check_usage,
        auto_record=auto_record,
        error_message=error_message
    )


async def check_membership_permission(
    feature: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    检查会员权限的依赖注入函数
    
    Args:
        feature: 功能名称
        current_user: 当前用户
        db: 数据库会话
    
    Returns:
        权限检查结果
    """
    return membership_service.check_feature_permission(
        db=db,
        user_id=current_user.id,
        feature=feature,
        check_usage=True
    )


async def get_membership_info(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取会员信息的依赖注入函数
    
    Args:
        current_user: 当前用户
        db: 数据库会话
    
    Returns:
        会员信息
    """
    return membership_service.get_user_membership_info(db, current_user.id)


class PermissionChecker:
    """权限检查器类"""
    
    @staticmethod
    def check_resume_export_permission(
        current_user: User = Depends(get_current_user),
        db: Session = Depends(get_db)
    ) -> Dict[str, Any]:
        """检查简历导出权限"""
        return membership_service.check_feature_permission(
            db=db,
            user_id=current_user.id,
            feature="resume_export",
            check_usage=True
        )
    
    @staticmethod
    def check_idphoto_permission(
        current_user: User = Depends(get_current_user),
        db: Session = Depends(get_db)
    ) -> Dict[str, Any]:
        """检查证件照生成权限"""
        return membership_service.check_feature_permission(
            db=db,
            user_id=current_user.id,
            feature="idphoto_generate",
            check_usage=True
        )
    
    @staticmethod
    def check_premium_template_permission(
        current_user: User = Depends(get_current_user),
        db: Session = Depends(get_db)
    ) -> Dict[str, Any]:
        """检查高级模板权限"""
        return membership_service.check_feature_permission(
            db=db,
            user_id=current_user.id,
            feature="premium_templates",
            check_usage=False  # 高级模板不限制使用次数，只检查权限
        )


# 常用的权限检查装饰器实例
# 注意：auto_record改为False，改用前端主动上报机制
require_resume_export = require_membership(
    feature="resume_export",
    check_usage=True,
    auto_record=False,  # 改为前端主动上报
    error_message="简历导出次数已达今日上限，升级会员享受无限制导出"
)

require_idphoto_generate = require_membership(
    feature="idphoto_generate",
    check_usage=True,
    auto_record=False,  # 改为前端主动上报
    error_message="证件照生成次数已达今日上限，升级会员享受无限制生成"
)

require_premium_template = require_membership(
    feature="premium_templates",
    check_usage=False,
    auto_record=False,  # 改为前端主动上报
    error_message="高级模板需要会员权限，请升级会员后使用"
)

require_watermark_free = require_membership(
    feature="watermark_free",
    check_usage=False,
    auto_record=False,  # 保持不自动记录
    error_message="无水印导出需要会员权限"
)
