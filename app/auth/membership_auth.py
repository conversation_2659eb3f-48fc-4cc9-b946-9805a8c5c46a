"""
会员权限认证模块
包含装饰器和依赖注入两种权限检查方式
"""
import functools
import logging
from typing import Callable, Optional, Dict, Any
from fastapi import HTTPException, status, Depends
from sqlalchemy.orm import Session

from app.database import get_db
from app.models import User
from app.auth.base import get_current_user
from app.services.membership_service import membership_service

logger = logging.getLogger(__name__)


class MembershipRequired:
    """
    会员权限检查装饰器 - 备用方式

    使用装饰器的方式进行权限检查，代码更简洁但灵活性较低。
    适用场景：
    1. 简单的权限检查，不需要获取详细的权限信息
    2. 权限检查失败时直接抛出异常即可
    3. 不需要根据权限结果进行复杂的业务逻辑处理

    注意：由于改为前端主动上报机制，auto_record参数已不再自动记录使用

    使用方法：
    @require_resume_export
    @router.post("/some-endpoint")
    async def some_function(
        current_user: User = Depends(get_current_user),
        db: Session = Depends(get_db)
    ):
        # 如果权限检查通过，直接执行业务逻辑
        # 如果权限检查失败，会自动抛出HTTPException
    """
    
    def __init__(
        self, 
        feature: str,
        check_usage: bool = True,
        auto_record: bool = True,
        error_message: Optional[str] = None
    ):
        """
        初始化会员权限检查装饰器
        
        Args:
            feature: 功能名称
            check_usage: 是否检查使用次数
            auto_record: 是否自动记录使用
            error_message: 自定义错误消息
        """
        self.feature = feature
        self.check_usage = check_usage
        self.auto_record = auto_record
        self.error_message = error_message or f"使用{feature}功能需要会员权限"
    
    def __call__(self, func: Callable) -> Callable:
        """装饰器实现"""
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取依赖注入的参数
            current_user = None
            db = None
            
            for value in kwargs.values():
                if isinstance(value, User):
                    current_user = value
                elif hasattr(value, 'query'):  # Session对象
                    db = value
            
            if not current_user or not db:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="权限检查失败：缺少必要参数"
                )
            
            # 检查功能权限
            permission_result = membership_service.check_feature_permission(
                db=db,
                user_id=current_user.id,
                feature=self.feature,
                check_usage=self.check_usage
            )
            
            if not permission_result["has_permission"]:
                # 构建错误响应
                error_detail = {
                    "error": "permission_denied",
                    "message": permission_result["reason"],
                    "feature": self.feature,
                    "is_member": permission_result["is_member"],
                    "upgrade_required": permission_result.get("upgrade_required", False),
                    "usage_info": permission_result.get("usage_info", {})
                }
                
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=error_detail
                )
            
            # 执行原函数
            result = await func(*args, **kwargs)

            # 注意：不再自动记录使用，改为前端主动上报
            # 前端需要在用户真正完成操作后调用 /user/action/report 接口
            if self.auto_record:
                logger.info(f"功能 {self.feature} 执行成功，等待前端主动上报使用记录")

            return result
        
        return wrapper


def require_membership(
    feature: str,
    check_usage: bool = True,
    auto_record: bool = True,
    error_message: Optional[str] = None
):
    """
    会员权限检查装饰器工厂函数
    
    Args:
        feature: 功能名称
        check_usage: 是否检查使用次数
        auto_record: 是否自动记录使用
        error_message: 自定义错误消息
    
    Returns:
        装饰器函数
    """
    return MembershipRequired(
        feature=feature,
        check_usage=check_usage,
        auto_record=auto_record,
        error_message=error_message
    )


async def check_membership_permission(
    feature: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    检查会员权限的依赖注入函数
    
    Args:
        feature: 功能名称
        current_user: 当前用户
        db: 数据库会话
    
    Returns:
        权限检查结果
    """
    return membership_service.check_feature_permission(
        db=db,
        user_id=current_user.id,
        feature=feature,
        check_usage=True
    )


async def get_membership_info(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取会员信息的依赖注入函数
    
    Args:
        current_user: 当前用户
        db: 数据库会话
    
    Returns:
        会员信息
    """
    return membership_service.get_user_membership_info(db, current_user.id)


class PermissionChecker:
    """
    权限检查器类 - 推荐使用方式

    使用依赖注入的方式进行权限检查，更灵活且易于测试。
    适用场景：
    1. 需要在API函数中获取权限检查结果进行后续处理
    2. 需要根据权限结果返回不同的响应内容
    3. 需要在权限检查失败时进行自定义处理

    使用方法：
    @router.post("/some-endpoint")
    async def some_function(
        permission_check: dict = Depends(PermissionChecker.check_resume_export_permission)
    ):
        if not permission_check["has_permission"]:
            # 自定义权限检查失败处理
            raise HTTPException(status_code=403, detail=permission_check)
        # 继续执行业务逻辑
    """
    
    @staticmethod
    def check_resume_export_permission(
        current_user: User = Depends(get_current_user),
        db: Session = Depends(get_db)
    ) -> Dict[str, Any]:
        """检查简历导出权限"""
        return membership_service.check_feature_permission(
            db=db,
            user_id=current_user.id,
            feature="resume_export",
            check_usage=True
        )
    
    @staticmethod
    def check_idphoto_permission(
        current_user: User = Depends(get_current_user),
        db: Session = Depends(get_db)
    ) -> Dict[str, Any]:
        """检查证件照生成权限"""
        return membership_service.check_feature_permission(
            db=db,
            user_id=current_user.id,
            feature="idphoto_generate",
            check_usage=True
        )
    
    @staticmethod
    def check_premium_template_permission(
        current_user: User = Depends(get_current_user),
        db: Session = Depends(get_db)
    ) -> Dict[str, Any]:
        """检查高级模板权限"""
        return membership_service.check_feature_permission(
            db=db,
            user_id=current_user.id,
            feature="premium_templates",
            check_usage=False  # 高级模板不限制使用次数，只检查权限
        )


# ============================================================================
# 权限检查方式选择建议：
#
# 1. 推荐使用 PermissionChecker (依赖注入方式)：
#    - 更灵活，可以获取详细的权限信息
#    - 可以根据权限结果进行自定义处理
#    - 易于测试和调试
#    - 当前项目中 resume.py 和 idphoto.py 已采用此方式
#
# 2. 备用使用装饰器方式：
#    - 代码更简洁，适合简单场景
#    - 权限检查失败直接抛出异常
#    - 适合不需要复杂权限逻辑的场景
# ============================================================================

# 常用的权限检查装饰器实例（备用方式）
# 注意：auto_record改为False，改用前端主动上报机制
require_resume_export = require_membership(
    feature="resume_export",
    check_usage=True,
    auto_record=False,  # 改为前端主动上报
    error_message="简历导出次数已达今日上限，升级会员享受无限制导出"
)

require_idphoto_generate = require_membership(
    feature="idphoto_generate",
    check_usage=True,
    auto_record=False,  # 改为前端主动上报
    error_message="证件照生成次数已达今日上限，升级会员享受无限制生成"
)

require_premium_template = require_membership(
    feature="premium_templates",
    check_usage=False,
    auto_record=False,  # 改为前端主动上报
    error_message="高级模板需要会员权限，请升级会员后使用"
)

require_watermark_free = require_membership(
    feature="watermark_free",
    check_usage=False,
    auto_record=False,  # 保持不自动记录
    error_message="无水印导出需要会员权限"
)
