"""
认证模块初始化
"""
# 从主认证模块导入
from ..auth import (
    create_access_token,
    verify_token,
    get_current_user,
    get_current_user_optional
)

# 从会员认证模块导入
from .membership_auth import (
    MembershipRequired,
    require_membership,
    check_membership_permission,
    get_membership_info,
    PermissionChecker,
    require_resume_export,
    require_idphoto_generate,
    require_premium_template,
    require_watermark_free
)

__all__ = [
    # 基础认证
    "create_access_token",
    "verify_token", 
    "get_current_user",
    "get_current_user_optional",
    
    # 会员认证
    "MembershipRequired",
    "require_membership",
    "check_membership_permission",
    "get_membership_info",
    "PermissionChecker",
    "require_resume_export",
    "require_idphoto_generate", 
    "require_premium_template",
    "require_watermark_free"
]
