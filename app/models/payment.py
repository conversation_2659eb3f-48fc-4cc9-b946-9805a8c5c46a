"""
支付相关数据模型
"""
from sqlalchemy import Column, Integer, String, Text, TIMESTAMP, Enum, JSON, Boolean, DECIMAL, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database import Base
import enum


class OrderStatus(enum.Enum):
    """订单状态枚举"""
    PENDING = "pending"      # 待支付
    PAID = "paid"           # 已支付
    CANCELLED = "cancelled"  # 已取消
    EXPIRED = "expired"     # 已过期
    REFUNDED = "refunded"   # 已退款


class PaymentStatus(enum.Enum):
    """支付状态枚举"""
    SUCCESS = "success"              # 支付成功
    FAILED = "failed"               # 支付失败
    REFUNDED = "refunded"           # 已退款
    PARTIAL_REFUNDED = "partial_refunded"  # 部分退款


class MembershipPlan(Base):
    """会员套餐表"""
    __tablename__ = "membership_plans"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    name = Column(String(100), nullable=False, comment="套餐名称")
    description = Column(Text, nullable=True, comment="套餐描述")
    price = Column(DECIMAL(10, 2), nullable=False, comment="价格(分)")
    original_price = Column(DECIMAL(10, 2), nullable=True, comment="原价(分)")
    duration_days = Column(Integer, nullable=False, comment="有效期(天)")
    features = Column(JSON, nullable=True, comment="套餐特权")
    is_active = Column(Boolean, default=True, nullable=False, comment="是否启用")
    is_recommended = Column(Boolean, default=False, nullable=False, comment="是否推荐")
    sort_order = Column(Integer, default=0, nullable=False, comment="排序")
    created_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        server_default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间"
    )

    # 关联关系
    orders = relationship("Order", back_populates="plan")
    user_memberships = relationship("UserMembership", back_populates="plan")


class Order(Base):
    """订单表"""
    __tablename__ = "orders"

    id = Column(String(32), primary_key=True, comment="订单号")
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, comment="用户ID")
    plan_id = Column(Integer, ForeignKey("membership_plans.id"), nullable=False, comment="套餐ID")
    amount = Column(DECIMAL(10, 2), nullable=False, comment="订单金额(分)")
    original_amount = Column(DECIMAL(10, 2), nullable=True, comment="原始金额(分)")
    discount_amount = Column(DECIMAL(10, 2), default=0, comment="优惠金额(分)")
    status = Column(Enum(OrderStatus), default=OrderStatus.PENDING, comment="订单状态")
    wx_prepay_id = Column(String(64), nullable=True, comment="微信预支付ID")
    wx_transaction_id = Column(String(32), nullable=True, comment="微信交易号")
    client_ip = Column(String(45), nullable=True, comment="客户端IP")
    user_agent = Column(Text, nullable=True, comment="用户代理")
    paid_at = Column(TIMESTAMP, nullable=True, comment="支付时间")
    expired_at = Column(TIMESTAMP, nullable=False, comment="订单过期时间")
    cancelled_at = Column(TIMESTAMP, nullable=True, comment="取消时间")
    cancel_reason = Column(String(255), nullable=True, comment="取消原因")
    created_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        server_default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间"
    )

    # 关联关系
    plan = relationship("MembershipPlan", back_populates="orders")
    payment_records = relationship("PaymentRecord", back_populates="order")
    user_memberships = relationship("UserMembership", back_populates="order")


class PaymentRecord(Base):
    """支付记录表"""
    __tablename__ = "payment_records"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    order_id = Column(String(32), ForeignKey("orders.id", ondelete="CASCADE"), nullable=False, comment="订单号")
    wx_transaction_id = Column(String(32), nullable=True, unique=True, comment="微信交易号")
    payment_method = Column(String(20), default="wechat", comment="支付方式")
    amount = Column(DECIMAL(10, 2), nullable=False, comment="支付金额(分)")
    status = Column(Enum(PaymentStatus), default=PaymentStatus.SUCCESS, comment="支付状态")
    callback_data = Column(JSON, nullable=True, comment="回调原始数据")
    refund_amount = Column(DECIMAL(10, 2), default=0, comment="退款金额(分)")
    refund_reason = Column(String(255), nullable=True, comment="退款原因")
    refunded_at = Column(TIMESTAMP, nullable=True, comment="退款时间")
    created_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="创建时间")

    # 关联关系
    order = relationship("Order", back_populates="payment_records")


class UserMembership(Base):
    """用户会员记录表"""
    __tablename__ = "user_memberships"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, comment="用户ID")
    order_id = Column(String(32), ForeignKey("orders.id"), nullable=False, comment="订单号")
    plan_id = Column(Integer, ForeignKey("membership_plans.id"), nullable=False, comment="套餐ID")
    start_date = Column(TIMESTAMP, nullable=False, comment="开始时间")
    end_date = Column(TIMESTAMP, nullable=False, comment="结束时间")
    is_active = Column(Boolean, default=True, nullable=False, comment="是否有效")
    auto_renew = Column(Boolean, default=False, nullable=False, comment="是否自动续费")
    activated_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="激活时间")
    deactivated_at = Column(TIMESTAMP, nullable=True, comment="失效时间")
    created_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="创建时间")

    # 关联关系
    order = relationship("Order", back_populates="user_memberships")
    plan = relationship("MembershipPlan", back_populates="user_memberships")


class PaymentLog(Base):
    """支付日志表"""
    __tablename__ = "payment_logs"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    order_id = Column(String(32), nullable=True, comment="订单号")
    user_id = Column(Integer, nullable=True, comment="用户ID")
    action = Column(String(50), nullable=False, comment="操作类型")
    status = Column(String(20), nullable=True, comment="操作状态")
    request_data = Column(JSON, nullable=True, comment="请求数据")
    response_data = Column(JSON, nullable=True, comment="响应数据")
    error_message = Column(Text, nullable=True, comment="错误信息")
    ip_address = Column(String(45), nullable=True, comment="IP地址")
    user_agent = Column(Text, nullable=True, comment="用户代理")
    created_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="创建时间")


# 更新模型导入
__all__ = [
    "MembershipPlan",
    "Order", 
    "PaymentRecord",
    "UserMembership",
    "PaymentLog",
    "OrderStatus",
    "PaymentStatus"
]
