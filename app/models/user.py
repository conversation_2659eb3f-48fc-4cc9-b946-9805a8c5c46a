"""
用户相关数据模型
"""
from sqlalchemy import Column, Integer, String, Text, TIMESTAMP, Enum, JSON, Boolean, Index
from sqlalchemy.sql import func
from app.database import Base
import enum


class User(Base):
    """用户表"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    openid = Column(String(100), unique=True, nullable=False, index=True, comment="微信openid")
    unionid = Column(String(100), nullable=True, comment="微信unionid")
    nickname = Column(String(100), nullable=True, comment="用户昵称")
    avatar_url = Column(Text, nullable=True, comment="头像URL")
    gender = Column(Integer, nullable=True, comment="性别：0未知，1男，2女")
    country = Column(String(50), nullable=True, comment="国家")
    province = Column(String(50), nullable=True, comment="省份")
    city = Column(String(50), nullable=True, comment="城市")
    is_member = Column(Boolean, default=False, nullable=False, comment="是否会员")
    created_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        server_default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间"
    )


class ActionType(enum.Enum):
    """用户行为类型枚举"""
    PREVIEW_RESUME = "preview_resume"
    DOWNLOAD_PDF = "download_pdf"
    USE_TEMPLATE = "use_template"
    EXPORT_JPEG = "export_jpeg"


class UserAction(Base):
    """用户行为记录表 - 扩展版本支持会员权益操作记录"""
    __tablename__ = "user_actions"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, nullable=False, index=True, comment="用户ID")
    action_type = Column(String(50), nullable=False, comment="行为类型")
    action_content = Column(JSON, nullable=True, comment="行为详细内容")

    # 扩展字段 - 支持更多会员权益操作记录
    feature_name = Column(String(50), nullable=True, index=True, comment="功能名称，如resume_export, idphoto_generate")
    resource_type = Column(String(30), nullable=True, comment="资源类型，如pdf, jpeg, idphoto, template")
    resource_id = Column(String(100), nullable=True, comment="资源ID，可以是文件ID、模板ID等")
    file_size = Column(Integer, nullable=True, comment="文件大小（字节）")
    file_format = Column(String(20), nullable=True, comment="文件格式，如pdf, jpeg, png")

    # 操作状态和结果
    operation_status = Column(String(20), default="completed", comment="操作状态：pending, completed, failed")
    error_message = Column(Text, nullable=True, comment="错误信息（如果操作失败）")

    # 会员权益相关
    is_member_action = Column(Boolean, default=False, comment="是否为会员权益操作")
    consumed_quota = Column(Integer, default=1, comment="消耗的配额数量")

    # 客户端信息
    client_info = Column(JSON, nullable=True, comment="客户端信息，如设备类型、版本等")
    ip_address = Column(String(45), nullable=True, comment="用户IP地址")

    # 兼容字段（保留向后兼容）
    template_id = Column(String(50), nullable=True, comment="使用的模板ID（兼容字段）")

    created_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        server_default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间"
    )


class FeedbackStatus(enum.Enum):
    """反馈状态枚举"""
    PENDING = "pending"
    REPLIED = "replied"
    CLOSED = "closed"


class Feedback(Base):
    """反馈表"""
    __tablename__ = "feedback"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, nullable=False, index=True, comment="用户ID")
    content = Column(Text, nullable=False, comment="反馈内容")
    contact_info = Column(String(200), nullable=True, comment="联系方式")
    status = Column(
        Enum(FeedbackStatus),
        default=FeedbackStatus.PENDING,
        comment="状态"
    )
    created_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        server_default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间"
    )


class FeedbackReply(Base):
    """反馈回复表"""
    __tablename__ = "feedback_replies"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    feedback_id = Column(Integer, nullable=False, index=True, comment="反馈ID")
    admin_name = Column(String(100), nullable=False, comment="管理员名称")
    reply_content = Column(Text, nullable=False, comment="回复内容")
    created_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="创建时间")


class FreeTemplate(Base):
    """免费简历模板表"""
    __tablename__ = "free_templates"

    id = Column(String(100), primary_key=True, index=True, comment="模板ID，如：blackWhite/10.jpg")
    batch_flag = Column(String(50), nullable=False, index=True, comment="批次标识，如：blackWhite")
    thumb_path = Column(String(255), nullable=False, comment="缩略图路径")
    baidu_url = Column(String(500), nullable=True, comment="百度网盘链接")
    baidu_pass = Column(String(20), nullable=True, comment="百度网盘提取码")
    quark_url = Column(String(500), nullable=True, comment="夸克网盘链接")
    quark_pass = Column(String(20), nullable=True, comment="夸克网盘提取码")
    download_count = Column(Integer, default=0, nullable=False, index=True, comment="下载次数")
    type = Column(String(20), default="word", nullable=False, index=True, comment="文件类型")
    sort_order = Column(Integer, default=0, nullable=False, index=True, comment="排序优先级，数值越小越靠前")
    created_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        server_default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间"
    )

    # 复合索引优化查询性能
    __table_args__ = (
        Index('idx_batch_type_sort', 'batch_flag', 'type', 'sort_order'),
        Index('idx_type_download', 'type', 'download_count'),
        Index('idx_sort_created', 'sort_order', 'created_at'),
    )


class ErrorReport(Base):
    """错误上报表 - 增强版本"""
    __tablename__ = "error_reports"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, nullable=True, index=True, comment="用户ID，可为空（未登录用户）")
    openid = Column(String(100), nullable=True, index=True, comment="微信openid，可为空")
    error_type = Column(String(100), nullable=False, index=True, comment="错误类型")
    error_message = Column(Text, nullable=False, comment="错误信息")
    error_context = Column(JSON, nullable=True, comment="错误上下文信息（包含所有动态内容）")

    # 时间字段
    timestamp = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp(), comment="错误发生时间")
    created_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="上报时间")
    reported_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="实际上报时间")

    # 请求相关信息
    request_url = Column(String(500), nullable=True, comment="请求URL")
    request_method = Column(String(10), nullable=True, comment="请求方法（GET/POST等）")
    request_params = Column(JSON, nullable=True, comment="请求参数")
    response_status = Column(Integer, nullable=True, comment="响应状态码")
    response_time = Column(Integer, nullable=True, comment="响应时间（毫秒）")

    # 用户会话信息
    session_id = Column(String(100), nullable=True, index=True, comment="会话ID")
    user_ip = Column(String(45), nullable=True, comment="用户IP地址")
    user_location = Column(JSON, nullable=True, comment="用户地理位置信息")

    # 应用状态信息
    app_state = Column(JSON, nullable=True, comment="应用状态信息")
    memory_usage = Column(Integer, nullable=True, comment="内存使用情况（MB）")
    battery_level = Column(Integer, nullable=True, comment="电池电量（百分比）")

    # 保留兼容字段（用于向后兼容）
    error_stack = Column(Text, nullable=True, comment="错误堆栈（兼容字段）")
    page_path = Column(String(255), nullable=True, comment="发生错误的页面路径（兼容字段）")
    user_agent = Column(Text, nullable=True, comment="用户代理信息（兼容字段）")
    device_info = Column(JSON, nullable=True, comment="设备信息（兼容字段）")
    app_version = Column(String(50), nullable=True, comment="应用版本（兼容字段）")
    system_info = Column(JSON, nullable=True, comment="系统信息（兼容字段）")
    network_type = Column(String(50), nullable=True, comment="网络类型（兼容字段）")

    # 索引优化查询性能
    __table_args__ = (
        Index('idx_error_type_created', 'error_type', 'created_at'),
        Index('idx_user_created', 'user_id', 'created_at'),
        Index('idx_openid_created', 'openid', 'created_at'),
        Index('idx_timestamp_created', 'timestamp', 'created_at'),
        Index('idx_session_created', 'session_id', 'created_at'),
        Index('idx_request_url', 'request_url'),
        Index('idx_response_status', 'response_status'),
    )


class ResumeThumb(Base):
    """简历缩略图表"""
    __tablename__ = "resume_thumbs"

    id = Column(String(100), primary_key=True, index=True)
    batch_flag = Column(String(50), nullable=False, index=True)
    thumb_path = Column(String(255), nullable=False)
    created_at = Column(TIMESTAMP, server_default=func.current_timestamp())
    updated_at = Column(TIMESTAMP, server_default=func.current_timestamp())
    sort_index = Column(Integer, default=0, nullable=False)
