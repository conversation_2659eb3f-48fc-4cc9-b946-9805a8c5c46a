"""
简历数据示例，用于测试API
"""

example_resume_data = {
    "moduleOrders": {
        "basicInfo": 0,
        "jobIntention": 1,
        "education": 2,
        "work": 3,
        "project": 4,
        "skills": 5,
        "awards": 6,
        "evaluation": 7
    },
    "basicInfo": {
        "moduleOrder": 0,
        "title": "基本信息",
        "name": "张三",
        "gender": "男",
        "phone": "13800138000",
        "photoUrl": '123',  # 实际使用时应提供Base64编码的图片
        "city": "北京",
        "email": "<EMAIL>",
        "wechat": "zhangsan123",
        "age": 28,
        "birthday": "1995-01-01",
        "customTitle1": "个人主页",
        "customContent1": "https://github.com/zhangsan"
    },
    "jobIntention": {
        "moduleOrder": 1,
        "title": "求职意向",
        "position": "Python后端工程师",
        "city": "北京",
        "salary": "15k-20k",
        "status": "在职，考虑机会"
    },
    "education": [
        {
            "school": "北京大学",
            "major": "计算机科学与技术",
            "degree": "本科",
            "start_date": "2014-09",
            "end_date": "2018-07",
            "description": "主修课程：数据结构、算法、计算机网络、操作系统、数据库系统等"
        }
    ],
    "work": [
        {
            "company": "ABC科技有限公司",
            "position": "Python开发工程师",
            "start_date": "2018-07",
            "end_date": "至今",
            "description": "1. 负责公司内部系统的开发和维护\n2. 使用FastAPI开发RESTful API\n3. 参与系统架构设计和优化"
        },
        {
            "company": "XYZ信息技术有限公司",
            "position": "实习开发工程师",
            "start_date": "2017-07",
            "end_date": "2018-01",
            "description": "1. 参与Web应用开发\n2. 编写单元测试\n3. 协助解决生产环境问题"
        }
    ],
    "project": [
        {
            "name": "企业内部管理系统",
            "role": "后端开发",
            "start_date": "2019-03",
            "end_date": "2019-12",
            "description": "1. 使用FastAPI和SQLAlchemy开发后端API\n2. 设计和优化数据库结构\n3. 实现认证和权限控制"
        },
        {
            "name": "数据分析平台",
            "role": "全栈开发",
            "start_date": "2020-01",
            "end_date": "2020-06",
            "description": "1. 使用Python处理和分析大量数据\n2. 开发数据可视化界面\n3. 优化系统性能"
        }
    ],
    "skills": [
        "Python", "FastAPI", "Flask", "Django", 
        "SQLAlchemy", "MySQL", "PostgreSQL", 
        "Redis", "Docker", "Git", 
        "Linux", "HTML/CSS", "JavaScript"
    ],
    "awards": [
        "2017年校级编程比赛一等奖",
        "2016年数学建模竞赛二等奖"
    ],
    "interests": [
        "编程", "读书", "旅行", "摄影"
    ],
    "evaluation": "我是一名有4年工作经验的Python后端开发工程师，擅长使用FastAPI、SQLAlchemy等框架进行Web应用开发。热爱编程，对新技术有浓厚兴趣，能够快速学习和适应。工作认真负责，善于团队协作，有良好的沟通能力。"
}

# 如何使用示例数据测试API


if __name__ == '__main__':
    import requests
    import json

    # 请确保服务器正在运行
    url = "http://localhost:8000/resume/render"

    # 发送请求
    response = requests.post(
        url, 
        json=example_resume_data,
        headers={"Content-Type": "application/json"}
    )

    # 检查响应
    if response.status_code == 200:
        # 保存HTML文件
        with open("resume.html", "w", encoding="utf-8") as f:
            f.write(response.text)
        print("简历HTML已保存到resume.html")
    else:
        print(f"错误: {response.status_code}")
        print(response.text)
