"""
用户行为记录API路由
"""
from fastapi import APIRouter, HTTPException, Depends, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
import logging

from app.database import get_db
from app.models import User, UserAction
from app.schemas.user import (
    UserActionCreate,
    UserActionInfo,
    ListResponse,
    ActionTypeEnum
)
from app.auth import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/user/action",
    tags=["用户行为"],
    responses={404: {"description": "未找到"}},
)

# @router.post("", response_model=MessageResponse)
# async def record_user_action(
#     action_data: UserActionCreate,
#     current_user: User = Depends(get_current_user),
#     db: Session = Depends(get_db)
# ):
#     """
#     记录用户行为
#     """
#     try:
#         # 创建用户行为记录
#         user_action = UserAction(
#             user_id=current_user.id,
#             action_type=action_data.action_type.value,
#             action_content=action_data.action_content,
#             template_id=action_data.template_id
#         )
        
#         db.add(user_action)
#         db.commit()
#         db.refresh(user_action)
        
#         logger.info(f"用户 {current_user.id} 记录行为: {action_data.action_type}")
        
#         return MessageResponse(message="用户行为记录成功")
        
#     except Exception as e:
#         logger.exception("记录用户行为异常")
#         db.rollback()
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail="记录用户行为失败"
#         )

# @router.get("", response_model=ListResponse)
# async def get_user_actions(
#     current_user: User = Depends(get_current_user),
#     db: Session = Depends(get_db),
#     action_type: Optional[ActionTypeEnum] = Query(None, description="行为类型筛选"),
#     template_id: Optional[str] = Query(None, description="模板ID筛选"),
#     limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
#     offset: int = Query(0, ge=0, description="偏移量")
# ):
#     """
#     获取用户行为记录列表
#     """
#     try:
#         # 构建查询
#         query = db.query(UserAction).filter(UserAction.user_id == current_user.id)
        
#         # 添加筛选条件
#         if action_type:
#             query = query.filter(UserAction.action_type == action_type.value)
        
#         if template_id:
#             query = query.filter(UserAction.template_id == template_id)
        
#         # 获取总数
#         total = query.count()
        
#         # 分页查询
#         actions = query.order_by(UserAction.created_at.desc()).offset(offset).limit(limit).all()
        
#         # 转换为响应模型
#         action_list = [UserActionInfo.model_validate(action) for action in actions]
        
#         return ListResponse(
#             total=total,
#             items=action_list
#         )
        
#     except Exception as e:
#         logger.exception("获取用户行为记录异常")
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail="获取用户行为记录失败"
#         )

# @router.get("/stats")
# async def get_user_action_stats(
#     current_user: User = Depends(get_current_user),
#     db: Session = Depends(get_db)
# ):
#     """
#     获取用户行为统计
#     """
#     try:
#         # 统计各种行为类型的数量
#         stats = {}
        
#         for action_type in ActionTypeEnum:
#             count = db.query(UserAction).filter(
#                 UserAction.user_id == current_user.id,
#                 UserAction.action_type == action_type.value
#             ).count()
#             stats[action_type.value] = count
        
#         # 统计模板使用情况
#         template_stats = db.query(
#             UserAction.template_id,
#             db.func.count(UserAction.id).label('count')
#         ).filter(
#             UserAction.user_id == current_user.id,
#             UserAction.template_id.isnot(None)
#         ).group_by(UserAction.template_id).all()
        
#         template_usage = {template_id: count for template_id, count in template_stats}
        
#         return {
#             "action_stats": stats,
#             "template_usage": template_usage,
#             "total_actions": sum(stats.values())
#         }
        
#     except Exception as e:
#         logger.exception("获取用户行为统计异常")
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail="获取用户行为统计失败"
#         )
