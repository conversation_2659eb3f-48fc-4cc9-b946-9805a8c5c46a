"""
免费简历模板相关路由
"""
from fastapi import APIRouter, Depends, HTTPException, Query, Request
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, asc
from typing import List, Optional
import logging

from app.database import get_db
from app.models.user import FreeTemplate, ResumeThumb
from app.schemas.free_template import (
    FreeTemplateInfo,
    FreeTemplateWechatInfo,
    FreeTemplateListResponse,
    FreeTemplateDownloadResponse,
    BatchTemplatesResponse,
    FreeTemplateCreate,
    FreeTemplateUpdate,
    FreeTemplateStyleInfo,
    FreeTemplateStyleListResponse
)

# 设置日志
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/free-templates", tags=["免费简历模板"])

# 允许的排序字段
ALLOWED_ORDER_FIELDS = {
    "id": FreeTemplate.id,
    "batch_flag": FreeTemplate.batch_flag,
    "download_count": FreeTemplate.download_count,
    "type": FreeTemplate.type,
    "sort_order": FreeTemplate.sort_order,
    "created_at": FreeTemplate.created_at,
    "updated_at": FreeTemplate.updated_at
}

def get_order_clause(order_by: str, order_direction: str):
    """
    构建安全的排序条件

    Args:
        order_by: 排序字段名
        order_direction: 排序方向 (asc/desc)

    Returns:
        SQLAlchemy排序条件
    """
    # 验证排序字段
    if order_by not in ALLOWED_ORDER_FIELDS:
        order_by = "sort_order"  # 默认排序字段

    order_field = ALLOWED_ORDER_FIELDS[order_by]

    # 验证排序方向
    if order_direction.lower() == "asc":
        return asc(order_field)
    else:
        return desc(order_field)

def get_style_sort_clause(sort: str):
    """
    构建样式模板的排序条件

    Args:
        sort: 排序方式 (popular/newest/rating)

    Returns:
        SQLAlchemy排序条件
    """
    if sort == "popular":
        # 按下载次数降序排列
        return desc(FreeTemplate.download_count)
    elif sort == "newest":
        # 按创建时间降序排列
        return desc(FreeTemplate.created_at)
    elif sort == "rating":
        # 暂时按下载次数降序排列，后续可以添加评分字段
        return desc(FreeTemplate.download_count)
    else:
        # 默认按sort_order升序排列
        return asc(FreeTemplate.sort_order)

def get_resume_thumb_sort_clause(sort: str):
    """
    构建简历缩略图的排序条件

    Args:
        sort: 排序方式 (popular/newest/rating)

    Returns:
        SQLAlchemy排序条件
    """
    if sort == "popular":
        # 按sort_index升序排列（数值越小越靠前，表示越热门）
        return asc(ResumeThumb.sort_index)
    elif sort == "newest":
        # 按创建时间降序排列
        return desc(ResumeThumb.created_at)
    elif sort == "rating":
        # 暂时按sort_index升序排列，后续可以添加评分字段
        return asc(ResumeThumb.sort_index)
    else:
        # 默认按sort_index升序排列
        return asc(ResumeThumb.sort_index)

def get_category_filter(category: str):
    """
    根据分类参数构建筛选条件

    Args:
        category: 分类名称 (business/creative/simple)

    Returns:
        筛选条件或None
    """
    # 基于现有的batch_flag字段进行分类映射
    category_mapping = {
        "business": ["bgdy"],      # 商务类 - 表格单页
        "creative": ["d100"],      # 创意类 - d100系列
        "simple": ["jydy"]         # 简约类 - 简约单页
    }

    if category in category_mapping:
        return FreeTemplate.batch_flag.in_(category_mapping[category])

    return None

def build_wechat_template_info(template: FreeTemplate, request: Request) -> dict:
    """
    构建微信端需要的精简模板信息

    Args:
        template: 数据库模板对象
        request: FastAPI请求对象

    Returns:
        微信端需要的精简模板信息字典
    """
    # 构建基础URL
    base_url = f"{request.url.scheme}://{request.url.netloc}"

    # 构建完整的缩略图URL
    thumb_url = f"{base_url}/static/{template.thumb_path}" if template.thumb_path else None

    # 转换为字典并添加thumb_url
    template_dict = {
        "id": template.id,
        # "batch_flag": template.batch_flag,
        # "thumb_path": template.thumb_path,
        "thumb_url": thumb_url,
        "baidu_url": template.baidu_url,
        "baidu_pass": template.baidu_pass,
        "quark_url": template.quark_url,
        "quark_pass": template.quark_pass,
        # "download_count": template.download_count,
        "type": template.type,
        # "created_at": template.created_at,
        # "updated_at": template.updated_at
    }

    return template_dict

def build_style_template_info(template: FreeTemplate, request: Request) -> dict:
    """
    构建样式模板需要的超精简模板信息

    Args:
        template: 数据库模板对象
        request: FastAPI请求对象

    Returns:
        样式模板需要的超精简模板信息字典
    """
    # 构建基础URL
    base_url = f"{request.url.scheme}://{request.url.netloc}"

    # 构建完整的缩略图URL
    thumb_url = f"{base_url}/static/{template.thumb_path}" if template.thumb_path else None

    # 只返回id和thumb_url
    template_dict = {
        "id": template.id,
        "thumb_url": thumb_url
    }

    return template_dict

def build_resume_thumb_info(thumb: ResumeThumb, request: Request) -> dict:
    """
    构建简历缩略图需要的超精简信息

    Args:
        thumb: 数据库缩略图对象
        request: FastAPI请求对象

    Returns:
        缩略图需要的超精简信息字典
    """
    # 构建基础URL
    base_url = f"{request.url.scheme}://{request.url.netloc}"

    # 构建完整的缩略图URL
    thumb_url = f"{base_url}/static/{thumb.thumb_path}" if thumb.thumb_path else None

    # 只返回id和thumb_url
    thumb_dict = {
        "id": thumb.id,
        "thumb_url": thumb_url
    }

    return thumb_dict

@router.get("/styles", response_model=FreeTemplateStyleListResponse)
async def get_style_templates(
    request: Request,
    skip: int = Query(0, ge=0, description="跳过的记录数，用于分页"),
    limit: int = Query(20, ge=1, le=50, description="每页返回的记录数，默认20，最大50"),
    sort: str = Query("popular", description="排序方式: popular(热门), newest(最新), rating(评分)"),
    db: Session = Depends(get_db)
):
    """
    获取简历样式模板列表

    用于在简历样式页面展示给用户选择的模板缩略图
    从 resume_thumbs 表中查询 batch_flag 为 "template" 的记录
    """
    try:
        # 构建查询，只查询 batch_flag 为 "template" 的记录
        query = db.query(ResumeThumb).filter(ResumeThumb.batch_flag == "template")

        # 获取总数
        total = query.count()

        # 构建排序条件
        sort_clause = get_resume_thumb_sort_clause(sort)

        # 分页查询，应用排序
        thumbs = query.order_by(sort_clause).offset(skip).limit(limit).all()

        # 构建简历缩略图需要的超精简信息列表
        template_list = [build_resume_thumb_info(thumb, request) for thumb in thumbs]

        # 计算是否还有更多数据
        has_more = (skip + limit) < total

        logger.info(f"获取简历样式模板列表成功，排序: {sort}, 总数: {total}, 返回: {len(thumbs)}")

        # 构建响应数据
        response_data = {
            "templates": template_list,
            "total": total,
            "skip": skip,
            "limit": limit,
            "has_more": has_more
        }

        return FreeTemplateStyleListResponse(
            code=200,
            message="获取成功",
            data=response_data
        )

    except Exception as e:
        logger.error(f"获取简历样式模板列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取简历样式模板列表失败: {str(e)}")

@router.get("/", response_model=FreeTemplateListResponse)
async def get_all_templates(
    request: Request,
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    batch_flag: Optional[str] = Query(None, description="批次标识筛选"),
    type: Optional[str] = Query(None, description="文件类型筛选"),
    order_by: str = Query("sort_order", description="排序字段: sort_order, id, batch_flag, download_count, type, created_at, updated_at"),
    order_direction: str = Query("asc", description="排序方向: asc(升序), desc(降序)"),
    db: Session = Depends(get_db)
):
    """
    获取所有免费简历模板列表

    用于微信端展示所有免费简历资源的缩略图
    """
    try:
        # 构建查询
        query = db.query(FreeTemplate)

        # 添加筛选条件
        if batch_flag:
            query = query.filter(FreeTemplate.batch_flag == batch_flag)
        if type:
            query = query.filter(FreeTemplate.type == type)

        # 构建排序条件
        order_clause = get_order_clause(order_by, order_direction)

        # 分页查询，应用排序
        templates = query.order_by(order_clause).offset(skip).limit(limit).all()

        # 只有在需要总数时才执行count查询（可以考虑缓存或异步获取）
        total = query.count()  # if skip == 0 else len(templates) + skip

        # 构建微信端需要的精简模板信息列表
        template_list = [build_wechat_template_info(template, request) for template in templates]

        logger.info(f"获取免费模板列表成功，总数: {total}, 返回: {len(templates)}")

        return FreeTemplateListResponse(
            total=total,
            templates=template_list
        )

    except Exception as e:
        logger.error(f"获取免费模板列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取模板列表失败: {str(e)}")

@router.get("/batches", response_model=List[str])
async def get_batch_list(db: Session = Depends(get_db)):
    """
    获取所有批次标识列表
    """
    try:
        batches = db.query(FreeTemplate.batch_flag).distinct().all()
        batch_list = [batch[0] for batch in batches]

        logger.info(f"获取批次列表成功，共 {len(batch_list)} 个批次")
        return batch_list

    except Exception as e:
        logger.error(f"获取批次列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取批次列表失败: {str(e)}")

@router.get("/batch/{batch_flag}", response_model=BatchTemplatesResponse)
async def get_templates_by_batch(
    request: Request,
    batch_flag: str,
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    order_by: str = Query("sort_order", description="排序字段: sort_order, id, batch_flag, download_count, type, created_at, updated_at"),
    order_direction: str = Query("asc", description="排序方向: asc(升序), desc(降序)"),
    db: Session = Depends(get_db)
):
    """
    根据批次标识获取模板列表
    """
    try:
        # 查询指定批次的模板
        query = db.query(FreeTemplate).filter(FreeTemplate.batch_flag == batch_flag)

        total = query.count()

        # 构建排序条件
        order_clause = get_order_clause(order_by, order_direction)

        templates = query.order_by(order_clause).offset(skip).limit(limit).all()

        if total == 0:
            raise HTTPException(status_code=404, detail=f"未找到批次 '{batch_flag}' 的模板")

        # 构建微信端需要的精简模板信息列表
        template_list = [build_wechat_template_info(template, request) for template in templates]

        logger.info(f"获取批次 '{batch_flag}' 模板成功，总数: {total}, 返回: {len(templates)}")

        return BatchTemplatesResponse(
            batch_flag=batch_flag,
            templates=template_list,
            total=total
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取批次模板失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取批次模板失败: {str(e)}")

@router.get("/{template_id:path}/download", response_model=FreeTemplateDownloadResponse)
async def get_download_links(template_id: str, db: Session = Depends(get_db)):
    """
    获取指定模板的下载链接

    微信端用户点击缩略图后调用此接口获取下载链接
    """
    try:
        # 查找模板
        template = db.query(FreeTemplate).filter(FreeTemplate.id == template_id).first()

        if not template:
            raise HTTPException(status_code=404, detail=f"未找到模板: {template_id}")

        # 增加下载次数
        template.download_count += 1
        db.commit()

        logger.info(f"获取模板下载链接成功: {template_id}, 下载次数: {template.download_count}")

        return FreeTemplateDownloadResponse(
            id=template.id,
            baidu_url=template.baidu_url,
            baidu_pass=template.baidu_pass,
            quark_url=template.quark_url,
            quark_pass=template.quark_pass,
            download_count=template.download_count,
            message="获取下载链接成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取下载链接失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取下载链接失败: {str(e)}")

@router.get("/stats/summary")
async def get_template_stats(db: Session = Depends(get_db)):
    """
    获取模板统计信息
    """
    try:
        # 总模板数
        total_templates = db.query(FreeTemplate).count()

        # 按批次统计
        batch_stats = db.query(
            FreeTemplate.batch_flag,
            func.count(FreeTemplate.id).label('count'),
            func.sum(FreeTemplate.download_count).label('total_downloads')
        ).group_by(FreeTemplate.batch_flag).all()

        # 按类型统计
        type_stats = db.query(
            FreeTemplate.type,
            func.count(FreeTemplate.id).label('count')
        ).group_by(FreeTemplate.type).all()

        # 总下载次数
        total_downloads = db.query(func.sum(FreeTemplate.download_count)).scalar() or 0

        stats = {
            "total_templates": total_templates,
            "total_downloads": int(total_downloads),
            "batch_stats": [
                {
                    "batch_flag": stat.batch_flag,
                    "template_count": int(stat.count),
                    "download_count": int(stat.total_downloads or 0)
                }
                for stat in batch_stats
            ],
            "type_stats": [
                {
                    "type": stat.type,
                    "count": int(stat.count)
                }
                for stat in type_stats
            ]
        }

        logger.info(f"获取模板统计信息成功")
        return JSONResponse(content=stats)

    except Exception as e:
        logger.error(f"获取模板统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@router.get("/{template_id:path}", response_model=FreeTemplateWechatInfo)
async def get_template_detail(request: Request, template_id: str, db: Session = Depends(get_db)):
    """
    获取指定模板的详细信息（微信端精简版）
    """
    try:
        template = db.query(FreeTemplate).filter(FreeTemplate.id == template_id).first()

        if not template:
            raise HTTPException(status_code=404, detail=f"未找到模板: {template_id}")

        # 构建微信端需要的精简模板信息
        template_info = build_wechat_template_info(template, request)

        logger.info(f"获取模板详情成功: {template_id}")
        return template_info

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取模板详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取模板详情失败: {str(e)}")

# # 管理接口（可选，用于后台管理）
# @router.post("/", response_model=FreeTemplateInfo)
# async def create_template(template: FreeTemplateCreate, db: Session = Depends(get_db)):
#     """
#     创建新的免费模板（管理接口）
#     """
#     try:
#         # 检查是否已存在
#         existing = db.query(FreeTemplate).filter(FreeTemplate.id == template.id).first()
#         if existing:
#             raise HTTPException(status_code=400, detail=f"模板ID已存在: {template.id}")

#         # 创建新模板
#         db_template = FreeTemplate(**template.dict())
#         db.add(db_template)
#         db.commit()
#         db.refresh(db_template)

#         logger.info(f"创建模板成功: {template.id}")
#         return FreeTemplateInfo.from_orm(db_template)

#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"创建模板失败: {str(e)}")
#         raise HTTPException(status_code=500, detail=f"创建模板失败: {str(e)}")

# @router.put("/{template_id}", response_model=FreeTemplateInfo)
# async def update_template(
#     template_id: str,
#     template_update: FreeTemplateUpdate,
#     db: Session = Depends(get_db)
# ):
#     """
#     更新模板信息（管理接口）
#     """
#     try:
#         template = db.query(FreeTemplate).filter(FreeTemplate.id == template_id).first()
#         if not template:
#             raise HTTPException(status_code=404, detail=f"未找到模板: {template_id}")

#         # 更新字段
#         update_data = template_update.dict(exclude_unset=True)
#         for field, value in update_data.items():
#             setattr(template, field, value)

#         db.commit()
#         db.refresh(template)

#         logger.info(f"更新模板成功: {template_id}")
#         return FreeTemplateInfo.from_orm(template)

#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"更新模板失败: {str(e)}")
#         raise HTTPException(status_code=500, detail=f"更新模板失败: {str(e)}")
