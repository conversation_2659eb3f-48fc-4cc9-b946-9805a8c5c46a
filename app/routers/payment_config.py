"""
支付配置管理API路由
"""
from fastapi import APIRouter, HTTPException, Depends, status, Request, Body
from sqlalchemy.orm import Session
from typing import Dict, Any, Optional, List
import logging
import os
from pathlib import Path

from app.database import get_db
from app.models import User
from app.auth import get_current_user
from config.wechat_pay_config import wechat_pay_settings, get_wechat_pay_config, validate_wechat_pay_config

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/payment/config",
    tags=["支付配置"],
    responses={404: {"description": "未找到"}},
)


@router.get("/wechat-pay")
async def get_wechat_pay_config(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取微信支付配置（敏感信息会被隐藏）
    """
    try:
        # 这里可以添加管理员权限检查
        # if not current_user.is_admin:
        #     raise HTTPException(status_code=403, detail="需要管理员权限")
        
        config = get_wechat_pay_config()
        
        # 隐藏敏感信息
        safe_config = {
            "appid": config["appid"],
            "mchid": config["mchid"][:4] + "****" + config["mchid"][-4:] if config["mchid"] else "",
            "cert_path": config["cert_path"],
            "key_path": config["key_path"],
            "cert_serial_no": config["cert_serial_no"][:8] + "****" if config["cert_serial_no"] else "",
            "notify_url": config["notify_url"],
            "sandbox": config["sandbox"],
            "api_base_url": config["api_base_url"],
            "order_expire_minutes": config["order_expire_minutes"],
            "order_prefix": config["order_prefix"],
            "payment_timeout": config["payment_timeout"],
            "max_retry_times": config["max_retry_times"],
        }
        
        # 检查证书文件是否存在
        cert_exists = os.path.exists(config["cert_path"])
        key_exists = os.path.exists(config["key_path"])
        
        return {
            "config": safe_config,
            "cert_exists": cert_exists,
            "key_exists": key_exists,
            "is_sandbox": config["sandbox"],
            "is_complete": bool(config["appid"] and config["mchid"] and config["api_v3_key"])
        }
        
    except Exception as e:
        logger.exception("获取微信支付配置异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取配置失败"
        )


@router.post("/wechat-pay")
async def update_wechat_pay_config(
    config_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user)
):
    """
    更新微信支付配置
    """
    try:
        # 这里可以添加管理员权限检查
        # if not current_user.is_admin:
        #     raise HTTPException(status_code=403, detail="需要管理员权限")
        
        # 获取环境变量文件路径
        env_file = Path(".env")
        
        # 读取现有环境变量
        env_vars = {}
        if env_file.exists():
            with open(env_file, "r") as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith("#") and "=" in line:
                        key, value = line.split("=", 1)
                        env_vars[key.strip()] = value.strip()
        
        # 更新配置
        updated_keys = []
        for key, value in config_data.items():
            if key == "WECHAT_PAY_APPID" and value:
                env_vars["WECHAT_PAY_APPID"] = value
                updated_keys.append(key)
            elif key == "WECHAT_PAY_MCHID" and value:
                env_vars["WECHAT_PAY_MCHID"] = value
                updated_keys.append(key)
            elif key == "WECHAT_PAY_API_V3_KEY" and value:
                env_vars["WECHAT_PAY_API_V3_KEY"] = value
                updated_keys.append(key)
            elif key == "WECHAT_PAY_CERT_SERIAL_NO" and value:
                env_vars["WECHAT_PAY_CERT_SERIAL_NO"] = value
                updated_keys.append(key)
            elif key == "WECHAT_PAY_NOTIFY_URL" and value:
                env_vars["WECHAT_PAY_NOTIFY_URL"] = value
                updated_keys.append(key)
            elif key == "WECHAT_PAY_SANDBOX":
                env_vars["WECHAT_PAY_SANDBOX"] = "true" if value else "false"
                updated_keys.append(key)
            elif key == "ORDER_EXPIRE_MINUTES" and value:
                env_vars["ORDER_EXPIRE_MINUTES"] = str(value)
                updated_keys.append(key)
            elif key == "ORDER_PREFIX" and value:
                env_vars["ORDER_PREFIX"] = value
                updated_keys.append(key)
        
        # 写入环境变量文件
        with open(env_file, "w") as f:
            for key, value in env_vars.items():
                f.write(f"{key}={value}\n")
        
        # 重新加载配置
        # 注意：在生产环境中，可能需要重启应用才能完全生效
        os.environ.update(env_vars)
        
        return {
            "success": True,
            "message": "微信支付配置已更新",
            "updated_keys": updated_keys
        }
        
    except Exception as e:
        logger.exception("更新微信支付配置异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新配置失败: {str(e)}"
        )


@router.post("/wechat-pay/validate")
async def validate_wechat_pay_configuration(
    current_user: User = Depends(get_current_user)
):
    """
    验证微信支付配置
    """
    try:
        # 这里可以添加管理员权限检查
        # if not current_user.is_admin:
        #     raise HTTPException(status_code=403, detail="需要管理员权限")
        
        try:
            validate_wechat_pay_config()
            return {
                "success": True,
                "message": "微信支付配置验证通过"
            }
        except ValueError as e:
            return {
                "success": False,
                "message": str(e)
            }
        
    except Exception as e:
        logger.exception("验证微信支付配置异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="验证配置失败"
        )


@router.post("/wechat-pay/upload-cert")
async def upload_wechat_pay_cert(
    request: Request,
    cert_type: str = Body(...),
    cert_content: str = Body(...),
    current_user: User = Depends(get_current_user)
):
    """
    上传微信支付证书
    """
    try:
        # 这里可以添加管理员权限检查
        # if not current_user.is_admin:
        #     raise HTTPException(status_code=403, detail="需要管理员权限")
        
        # 获取证书目录
        cert_dir = Path("certs")
        if not cert_dir.exists():
            cert_dir.mkdir(parents=True)
        
        # 根据证书类型确定文件路径
        if cert_type == "cert":
            file_path = cert_dir / "apiclient_cert.pem"
        elif cert_type == "key":
            file_path = cert_dir / "apiclient_key.pem"
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的证书类型"
            )
        
        # 写入证书文件
        with open(file_path, "w") as f:
            f.write(cert_content)
        
        # 更新环境变量
        env_file = Path(".env")
        env_vars = {}
        
        if env_file.exists():
            with open(env_file, "r") as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith("#") and "=" in line:
                        key, value = line.split("=", 1)
                        env_vars[key.strip()] = value.strip()
        
        # 更新证书路径
        if cert_type == "cert":
            env_vars["WECHAT_PAY_CERT_PATH"] = str(file_path)
        elif cert_type == "key":
            env_vars["WECHAT_PAY_KEY_PATH"] = str(file_path)
        
        # 写入环境变量文件
        with open(env_file, "w") as f:
            for key, value in env_vars.items():
                f.write(f"{key}={value}\n")
        
        return {
            "success": True,
            "message": f"微信支付{cert_type}证书上传成功",
            "file_path": str(file_path)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("上传微信支付证书异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"上传证书失败: {str(e)}"
        )


@router.get("/environment")
async def get_environment_info(
    current_user: User = Depends(get_current_user)
):
    """
    获取环境信息
    """
    try:
        # 这里可以添加管理员权限检查
        # if not current_user.is_admin:
        #     raise HTTPException(status_code=403, detail="需要管理员权限")
        
        # 获取环境信息
        import platform
        import sys
        
        return {
            "system": platform.system(),
            "python_version": sys.version,
            "hostname": platform.node(),
            "is_sandbox": wechat_pay_settings.WECHAT_PAY_SANDBOX,
            "app_mode": os.environ.get("APP_MODE", "development"),
            "api_base_url": wechat_pay_settings.WECHAT_PAY_API_BASE_URL
        }
        
    except Exception as e:
        logger.exception("获取环境信息异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取环境信息失败"
        )
