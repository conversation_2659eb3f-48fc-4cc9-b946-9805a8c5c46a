from fastapi import APIRouter, HTTPException, Request, Query, Response, Body, Depends
from fastapi.responses import HTMLResponse, JSONResponse
from sqlalchemy.orm import Session
from app.schemas.resume import ResumeData
from app.services.resume_renderer import ResumeRenderer
from app.services.pdf_service import PDFService
from app.services.temp_file_service import get_temp_file_service
from app.database import get_db
from app.models import User, UserAction
from app.auth import get_current_user_optional, get_current_user
from app.auth.membership_auth import Permission<PERSON>hecker
from typing import Optional, List
import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/resumerender",
    tags=["resumerender"],
    responses={404: {"description": "未找到"}},
)

# 初始化简历渲染器和PDF服务
resume_renderer = ResumeRenderer()
pdf_service = PDFService()
temp_file_service = get_temp_file_service()

def record_user_action(db: Session, user: Optional[User], action_type: str, template_id: Optional[str] = None, action_content: Optional[dict] = None):
    """记录用户行为"""
    if user:
        try:
            user_action = UserAction(
                user_id=user.id,
                action_type=action_type,
                template_id=template_id,
                action_content=action_content
            )
            db.add(user_action)
            db.commit()
            logger.info(f"记录用户 {user.id} 行为: {action_type}")
        except Exception as e:
            logger.warning(f"记录用户行为失败: {e}")
            db.rollback()

# @router.post("/render", response_class=HTMLResponse)
# async def render_resume(
#     resume_data: ResumeData,
#     template_name: str = Query("templateA02.html", description="模板名称"),
#     theme_color: Optional[str] = Query(None, description="主题颜色")
# ):
#     """
#     接收简历数据并渲染HTML
#     """
#     try:
#         # 配置主题
#         theme_config = {}
#         if theme_color:
#             theme_config["theme_color"] = theme_color

#         # 渲染简历模板
#         html_content = resume_renderer.render_template(resume_data, template_name, theme_config)
#         return HTMLResponse(content=html_content, status_code=200)
#     except Exception as e:
#         logger.exception("渲染简历时出错")
#         raise HTTPException(status_code=500, detail=f"渲染简历时出错: {str(e)}")

@router.post("/preview", response_class=JSONResponse)
async def preview_resume(
    request: Request,
    resume_data: dict = Body(..., embed=True),
    template_id: str = Body(..., embed=True, description="模板ID"),
    theme_config: dict = Body(..., embed=True, description="主题配置"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    预览简历数据，返回处理后的JSON和HTML片段
    """
    logger.info("=== 开始处理简历预览请求 ===")
    logger.info(f"接收到的模板ID: {template_id}")
    logger.info(f"接收到的主题配置: {theme_config}")
    logger.info(f"用户ID: {current_user.id if current_user else '未登录'}")

    try:
        resume_data = ResumeData(**resume_data)

        # 渲染简历模板
        logger.info(f"开始渲染预览模板: {template_id}")
        html_content = resume_renderer.render_template(resume_data, template_id, theme_config)
        logger.info(f"预览模板渲染完成，HTML长度: {len(html_content)}")

        # 记录用户行为
        record_user_action(
            db=db,
            user=current_user,
            action_type="preview_resume",
            template_id=template_id,
            action_content={
                "template_id": template_id,
                "theme_config": theme_config
            }
        )

        # 返回处理后的简历数据和HTML
        return JSONResponse(
            content={
                "message": "简历预览生成成功",
                "html": html_content,
                "template_id": template_id,
                "theme_config": theme_config,
                "resume_data": resume_data.model_dump()
            }
        )
    except Exception as e:
        logger.exception("预览简历时出错")
        raise HTTPException(status_code=500, detail=f"预览简历时出错: {str(e)}")

@router.post("/export-pdf", response_class=Response)
async def export_pdf(
    request: Request,
    resume_data: dict = Body(..., embed=True),
    template_id: str = Body(..., embed=True, description="模板ID"),
    theme_config: dict = Body(..., embed=True, description="主题配置"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    permission_check: dict = Depends(PermissionChecker.check_resume_export_permission)
):
    """
    导出简历为PDF文件
    """
    # 检查会员权限
    if not permission_check["has_permission"]:
        raise HTTPException(
            status_code=403,
            detail={
                "error": "permission_denied",
                "message": permission_check["reason"],
                "is_member": permission_check["is_member"],
                "upgrade_required": permission_check.get("upgrade_required", False),
                "usage_info": permission_check.get("usage_info", {})
            }
        )

    logger.info("=== 开始处理PDF导出请求 ===")
    logger.info(f"接收到的模板ID: {template_id}")
    logger.info(f"接收到的主题配置: {theme_config}")
    logger.info(f"用户ID: {current_user.id if current_user else '未登录'}")
    logger.info(f"权限检查: {permission_check}")

    # print(f'resume_data: {resume_data}')
    # print(f'template_id: {template_id}')
    # print(f'theme_config: {theme_config}')

    resume_data_obj = ResumeData(**resume_data)
    try:
        # 生成访问URL的基础URL
        base_url = f"{request.url.scheme}://{request.url.netloc}"

        # 尝试从temp_file_service的hash缓存获取PDF URL
        cached_pdf_url = temp_file_service.get_cached_file_url(
            resume_data=resume_data,
            template_id=template_id,
            theme_config=theme_config,
            file_type="pdf",
            base_url=base_url
        )

        if cached_pdf_url:
            logger.info(f"PDF缓存命中，直接返回URL: {cached_pdf_url}")

            # 记录用户行为
            record_user_action(
                db=db,
                user=current_user,
                action_type="download_pdf",
                template_id=template_id,
                action_content={
                    "template_id": template_id,
                    "theme_config": theme_config,
                    "from_cache": True
                }
            )

            # 返回JSON响应包含PDF URL
            return JSONResponse(
                content={
                    "success": True,
                    "message": "PDF生成成功",
                    "data": {
                        "pdf_url": cached_pdf_url,
                        "template_id": template_id
                    },
                    "cache_info": {
                        "cached": True,
                        "cache_type": "temp_file_hash"
                    }
                }
            )

        # 渲染简历模板
        logger.info(f"开始渲染模板: {template_id}")
        html_content = resume_renderer.render_template(
            resume_data_obj,
            template_id,
            theme_config
        )
        logger.info(f"模板渲染完成，HTML长度: {len(html_content)}")

        # 检查PDF服务健康状态
        health_status = await pdf_service.check_health()
        if health_status.get("status") != "ok":
            raise HTTPException(
                status_code=503,
                detail=f"PDF服务不可用: {health_status.get('message', '未知错误')}"
            )

        # 设置PDF选项
        # filename = '个人简历制作花花版.pdf'
        filename = 'resume.pdf'
        pdf_options = {
            "filename": filename
        }

        logger.info("发送HTML内容到PDF服务转换")
        # 转换为PDF
        pdf_result = await pdf_service.html_to_pdf(html_content, pdf_options)

        # 检查结果
        if isinstance(pdf_result, dict) and "error" in pdf_result:
            raise HTTPException(
                status_code=500,
                detail=f"PDF生成失败: {pdf_result.get('message', pdf_result.get('error', '未知错误'))}"
            )

        # 保存PDF为临时文件，并建立hash缓存
        pdf_file_id = temp_file_service.save_pdf_file(
            pdf_data=pdf_result,
            resume_data=resume_data,
            template_id=template_id,
            theme_config=theme_config
        )

        # 生成访问URL
        pdf_url = temp_file_service.get_file_url(pdf_file_id, base_url)

        if not pdf_url:
            raise HTTPException(
                status_code=500,
                detail="生成PDF临时文件URL失败"
            )

        # 记录用户行为
        record_user_action(
            db=db,
            user=current_user,
            action_type="download_pdf",
            template_id=template_id,
            action_content={
                "template_id": template_id,
                "theme_config": theme_config,
                "pdf_file_id": pdf_file_id,
                "from_cache": False
            }
        )

        logger.info("PDF生成成功，返回URL")
        # 返回JSON响应包含PDF URL
        return JSONResponse(
            content={
                "success": True,
                "message": "PDF生成成功",
                "data": {
                    "pdf_url": pdf_url,
                    "file_id": pdf_file_id,
                    "template_id": template_id
                },
                "cache_info": {
                    "cached": False,
                    "cache_type": "temp_file_hash"
                }
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("导出PDF时出错")
        raise HTTPException(status_code=500, detail=f"导出PDF时出错: {str(e)}")

@router.post("/export-jpeg", response_class=JSONResponse)
async def export_jpeg(
    request: Request,
    resume_data: dict = Body(..., embed=True),
    template_id: str = Body(..., embed=True, description="模板ID"),
    theme_config: dict = Body(..., embed=True, description="主题配置"),
    quality: int = Body(75, embed=True, description="图片压缩质量(1-100)"),
    max_width: int = Body(1200, embed=True, description="最大宽度(A4纸宽度)"),
    max_height: int = Body(3600, embed=True, description="最大高度(弹性高度，保持A4比例)"),

    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    permission_check: dict = Depends(PermissionChecker.check_resume_export_permission)
):
    """
    导出简历为JPEG图片，返回临时文件URL
    """
    # 检查会员权限
    if not permission_check["has_permission"]:
        raise HTTPException(
            status_code=403,
            detail={
                "error": "permission_denied",
                "message": permission_check["reason"],
                "is_member": permission_check["is_member"],
                "upgrade_required": permission_check.get("upgrade_required", False),
                "usage_info": permission_check.get("usage_info", {})
            }
        )

    logger.info("=== 开始处理JPEG导出请求 ===")
    logger.info(f"接收到的模板ID: {template_id}")
    logger.info(f"接收到的主题配置: {theme_config}")
    logger.info(f"压缩参数: quality={quality}, max_width={max_width}, max_height={max_height}")
    logger.info(f"用户ID: {current_user.id if current_user else '未登录'}")
    logger.info(f"权限检查: {permission_check}")

    # print(f'\ncustome: {resume_data.get("custom1", "")}')
    # print(f'custom3: {resume_data.get("custom3", "")}')
    # print(f'template_id: {template_id}')
    # print(f'theme_config: {theme_config}')

    resume_data_obj = ResumeData(**resume_data)

    try:
        # 构建缓存的额外参数
        cache_extra_params = {
            "quality": quality,
            "max_width": max_width,
            "max_height": max_height
        }

        # 生成访问URL的基础URL
        base_url = f"{request.url.scheme}://{request.url.netloc}"

        # 尝试从temp_file_service的hash缓存获取URL
        cached_url = temp_file_service.get_cached_file_url(
            resume_data=resume_data,
            template_id=template_id,
            theme_config=theme_config,
            file_type="image",
            base_url=base_url,
            extra_params=cache_extra_params
        )

        if cached_url:
            logger.info(f"临时文件缓存命中，直接返回URL: {cached_url}")

            # 记录用户行为
            record_user_action(
                db=db,
                user=current_user,
                action_type="export_jpeg",
                template_id=template_id,
                action_content={
                    "template_id": template_id,
                    "theme_config": theme_config,
                    "compression_settings": cache_extra_params,
                    "from_cache": True
                }
            )

            # 获取统计信息
            stats = temp_file_service.get_stats()
            # print('----------')
            # print('from cache')
            # print('----------')

            return JSONResponse(
                content={
                    "success": True,
                    "message": "简历图片生成成功",
                    "data": {
                        "file_id": "cached",
                        "image_url": cached_url,
                        "template_id": template_id,
                        "compression_settings": cache_extra_params
                    },
                    "stats": {
                        "total_temp_files": stats["total_files"],
                        "storage_saved_mb": round(stats["storage_saved_bytes"] / 1024 / 1024, 2)
                    },
                    "cache_info": {
                        "cached": True,
                        "cache_type": "temp_file_hash"
                    }
                }
            )

        # 渲染简历模板
        logger.info(f"开始渲染模板: {template_id}")
        html_content = resume_renderer.render_template(
            resume_data_obj,
            template_id,
            theme_config
        )
        logger.info(f"模板渲染完成，HTML长度: {len(html_content)}")

        # save to debug
        debug_filename = f'output/resume_{template_id}_jpeg.html'
        with open(debug_filename, 'w') as f:
            f.write(html_content)
        logger.info(f"调试文件已保存: {debug_filename}")

        # 检查PDF服务健康状态
        health_status = await pdf_service.check_health()
        if health_status.get("status") != "ok":
            raise HTTPException(
                status_code=503,
                detail=f"转换服务不可用: {health_status.get('message', '未知错误')}"
            )

        # 设置JPEG选项 - 使用高质量生成原始图片
        jpeg_options = {
            "filename": 'resume.jpeg',
            "imageOptions": {
                "type": "jpeg",
                "quality": 95,  # 使用高质量生成原始图片
                "fullPage": True
            }
        }

        logger.info("发送HTML内容到转换服务生成JPEG")
        # 转换为JPEG
        export_result = await pdf_service.html_to_jpeg(html_content, jpeg_options)

        # 检查结果
        if isinstance(export_result, dict) and "error" in export_result:
            raise HTTPException(
                status_code=500,
                detail=f"JPEG生成失败: {export_result.get('message', export_result.get('error', '未知错误'))}"
            )

        logger.info("JPEG生成成功，开始压缩并保存临时文件")

        # 确保 export_result 是 bytes 类型
        if not isinstance(export_result, bytes):
            raise HTTPException(
                status_code=500,
                detail="JPEG生成结果格式错误"
            )

        # 保存压缩后的临时文件，并建立hash缓存
        file_id = temp_file_service.save_compressed_image(
            image_data=export_result,
            quality=quality,
            max_width=max_width,
            max_height=max_height,
            resume_data=resume_data,
            template_id=template_id,
            theme_config=theme_config
        )

        # 生成访问URL
        base_url = f"{request.url.scheme}://{request.url.netloc}"
        file_url = temp_file_service.get_file_url(file_id, base_url)

        if not file_url:
            raise HTTPException(
                status_code=500,
                detail="生成临时文件URL失败"
            )

        # 记录用户行为
        record_user_action(
            db=db,
            user=current_user,
            action_type="export_jpeg",
            template_id=template_id,
            action_content={
                "template_id": template_id,
                "theme_config": theme_config,
                "file_id": file_id,
                "compression_settings": {
                    "quality": quality,
                    "max_width": max_width,
                    "max_height": max_height
                }
            }
        )

        # 获取文件统计信息
        stats = temp_file_service.get_stats()

        logger.info(f"JPEG压缩完成，文件ID: {file_id}, URL: {file_url}")

        # 返回JSON响应包含图片URL
        return JSONResponse(
            content={
                "success": True,
                "message": "简历图片生成成功",
                "data": {
                    "file_id": file_id,
                    "image_url": file_url,
                    "template_id": template_id,
                    "compression_settings": {
                        "quality": quality,
                        "max_width": max_width,
                        "max_height": max_height
                    }
                },
                "stats": {
                    "total_temp_files": stats["total_files"],
                    "storage_saved_mb": round(stats["storage_saved_bytes"] / 1024 / 1024, 2)
                },
                "cache_info": {
                    "cached": False,
                    "cache_type": "temp_file_hash"
                }
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("导出JPEG时出错")
        raise HTTPException(status_code=500, detail=f"导出JPEG时出错: {str(e)}")

@router.get("/temp-image/{file_id}")
async def get_temp_image(file_id: str):
    """
    获取临时图片文件
    """
    try:
        file_path = temp_file_service.get_file_path(file_id)
        if not file_path or not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="文件不存在或已过期")

        # 读取文件内容
        with open(file_path, 'rb') as f:
            content = f.read()

        return Response(
            content=content,
            media_type="image/jpeg",
            headers={
                "Cache-Control": "public, max-age=3600",  # 缓存1小时
                "Content-Disposition": f"inline; filename={file_id}.jpg"
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"获取临时文件失败: {file_id}")
        raise HTTPException(status_code=500, detail=f"获取文件失败: {str(e)}")

@router.get("/temp-pdf/{file_id}")
async def get_temp_pdf(file_id: str):
    """
    获取临时PDF文件
    """
    try:
        file_path = temp_file_service.get_file_path(file_id)
        if not file_path or not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="文件不存在或已过期")

        # 读取文件内容
        with open(file_path, 'rb') as f:
            content = f.read()

        return Response(
            content=content,
            media_type="application/pdf",
            headers={
                "Cache-Control": "public, max-age=3600",  # 缓存1小时
                "Content-Disposition": f"attachment; filename={file_id}.pdf"
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"获取临时PDF文件失败: {file_id}")
        raise HTTPException(status_code=500, detail=f"获取PDF文件失败: {str(e)}")



















@router.get("/templates", response_class=JSONResponse)
async def get_templates():
    """
    获取所有可用的模板列表
    """
    try:
        # 获取模板目录
        template_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "app", "templates")

        # 获取所有html文件
        templates = [f for f in os.listdir(template_dir) if f.endswith('.html')]

        return JSONResponse(
            content={
                "message": "获取模板列表成功",
                "templates": templates
            }
        )
    except Exception as e:
        logger.exception("获取模板列表时出错")
        raise HTTPException(status_code=500, detail=f"获取模板列表时出错: {str(e)}")