"""
健康检查API路由
"""
from fastapi import APIRouter, HTTPException, Depends, status, Request
from sqlalchemy.orm import Session
import logging
import time
from datetime import datetime

from app.database import get_db
from app.models import User
from app.auth import get_current_user
from app.services.config_health_check import config_health_check_service

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/health",
    tags=["健康检查"],
    responses={404: {"description": "未找到"}},
)


@router.get("/ping")
async def ping():
    """
    简单的健康检查接口
    """
    return {
        "status": "ok",
        "timestamp": datetime.now().isoformat(),
        "message": "服务正常运行"
    }


@router.get("/check")
async def health_check(
    request: Request,
    db: Session = Depends(get_db)
):
    """
    完整的健康检查接口
    """
    try:
        start_time = time.time()
        
        # 检查数据库连接
        try:
            db_result = db.execute("SELECT 1").fetchone()
            db_status = "ok" if db_result else "error"
        except Exception as e:
            db_status = "error"
            db_error = str(e)
        
        # 获取请求信息
        client_ip = request.client.host if request.client else None
        user_agent = request.headers.get("User-Agent")
        
        # 构建响应
        response = {
            "status": "ok" if db_status == "ok" else "error",
            "timestamp": datetime.now().isoformat(),
            "uptime": time.time() - start_time,
            "components": {
                "database": {
                    "status": db_status,
                    "error": db_error if db_status == "error" else None
                },
                "api": {
                    "status": "ok"
                }
            },
            "request": {
                "client_ip": client_ip,
                "user_agent": user_agent
            }
        }
        
        return response
        
    except Exception as e:
        logger.exception("健康检查异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="健康检查失败"
        )


@router.get("/payment-config")
async def payment_config_health_check(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    支付配置健康检查
    """
    try:
        # 这里可以添加管理员权限检查
        # if not current_user.is_admin:
        #     raise HTTPException(status_code=403, detail="需要管理员权限")
        
        # 运行所有健康检查
        check_results = config_health_check_service.run_all_checks()
        
        return check_results
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("支付配置健康检查异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="健康检查失败"
        )


@router.get("/config-summary")
async def get_config_summary(
    current_user: User = Depends(get_current_user)
):
    """
    获取配置摘要
    """
    try:
        # 这里可以添加管理员权限检查
        # if not current_user.is_admin:
        #     raise HTTPException(status_code=403, detail="需要管理员权限")
        
        # 获取配置摘要
        summary = config_health_check_service.get_config_summary()
        
        return {
            "success": True,
            "data": summary,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("获取配置摘要异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取配置摘要失败"
        )
