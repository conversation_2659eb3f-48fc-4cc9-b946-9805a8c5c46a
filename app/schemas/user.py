"""
用户相关的数据模式
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class UserGender(int, Enum):
    """用户性别枚举"""
    UNKNOWN = 0
    MALE = 1
    FEMALE = 2

class ActionTypeEnum(str, Enum):
    """用户行为类型枚举"""
    PREVIEW_RESUME = "preview_resume"
    DOWNLOAD_PDF = "download_pdf"
    USE_TEMPLATE = "use_template"
    EXPORT_JPEG = "export_jpeg"
    GENERATE_IDPHOTO = "generate_idphoto"

class FeedbackStatusEnum(str, Enum):
    """反馈状态枚举"""
    PENDING = "pending"
    REPLIED = "replied"
    CLOSED = "closed"

# 微信登录相关模式
class WeChatLoginRequest(BaseModel):
    """微信登录请求"""
    code: str = Field(..., description="微信登录code")
    user_info: Optional[Dict[str, Any]] = Field(None, description="用户信息")

class WeChatLoginResponse(BaseModel):
    """微信登录响应"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    expires_in: int = Field(..., description="令牌过期时间（秒）")
    user_info: "UserInfo" = Field(..., description="用户信息")

# 用户相关模式
class UserBase(BaseModel):
    """用户基础信息"""
    nickname: Optional[str] = None
    avatar_url: Optional[str] = None
    gender: Optional[UserGender] = None
    country: Optional[str] = None
    province: Optional[str] = None
    city: Optional[str] = None
    is_member: Optional[bool] = None

class UserCreate(UserBase):
    """创建用户"""
    openid: str = Field(..., description="微信openid")
    unionid: Optional[str] = None

class UserUpdate(UserBase):
    """更新用户信息"""
    pass

class UserInfo(UserBase):
    """用户信息响应"""
    id: int
    openid: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class MemberStatusResponse(BaseModel):
    """会员状态响应"""
    is_member: bool = Field(..., description="是否会员")
    openid: str = Field(..., description="用户openid")
    message: str = Field(..., description="状态描述")

# 用户行为相关模式
class UserActionCreate(BaseModel):
    """创建用户行为记录"""
    action_type: ActionTypeEnum = Field(..., description="行为类型")
    action_content: Optional[Dict[str, Any]] = Field(None, description="行为详细内容")
    template_id: Optional[str] = Field(None, description="模板ID")

class UserActionInfo(BaseModel):
    """用户行为信息"""
    id: int
    user_id: int
    action_type: str
    action_content: Optional[Dict[str, Any]]
    template_id: Optional[str]
    created_at: datetime

    class Config:
        from_attributes = True

# 反馈相关模式
class FeedbackCreate(BaseModel):
    """创建反馈"""
    content: str = Field(..., min_length=1, max_length=2000, description="反馈内容")
    contact_info: Optional[str] = Field(None, max_length=200, description="联系方式")

class FeedbackUpdate(BaseModel):
    """更新反馈状态"""
    status: FeedbackStatusEnum = Field(..., description="反馈状态")

class FeedbackReplyCreate(BaseModel):
    """创建反馈回复"""
    reply_content: str = Field(..., min_length=1, max_length=2000, description="回复内容")
    admin_name: str = Field(..., max_length=100, description="管理员名称")

class FeedbackReplyInfo(BaseModel):
    """反馈回复信息"""
    id: int
    admin_name: str
    reply_content: str
    created_at: datetime

    class Config:
        from_attributes = True

class FeedbackInfo(BaseModel):
    """反馈信息"""
    id: int
    user_id: int
    content: str
    contact_info: Optional[str]
    status: FeedbackStatusEnum
    created_at: datetime
    updated_at: datetime
    replies: List[FeedbackReplyInfo] = []

    class Config:
        from_attributes = True

class MessageResponse(BaseModel):
    """消息响应"""
    message: str = Field(..., description="响应消息")

class ListResponse(BaseModel):
    """列表响应"""
    total: int = Field(..., description="总数量")
    items: List[Any] = Field(..., description="数据列表")

# 错误上报相关模式 - 简化版本
class ErrorReportCreate(BaseModel):
    """错误上报创建模式 - 简化版本，匹配客户端格式"""
    error_type: str = Field(..., description="错误类型")
    error_message: str = Field(..., description="错误信息")
    error_context: Optional[Dict[str, Any]] = Field(None, description="错误上下文信息（包含所有动态内容）")


class ErrorReportBatch(BaseModel):
    """批量错误上报模式 - 简化版本"""
    errors: List[ErrorReportCreate] = Field(..., description="错误列表")

class ErrorReportInfo(BaseModel):
    """错误上报信息模式 - 增强版本"""
    id: int
    user_id: Optional[int]
    openid: Optional[str]
    error_type: str
    error_message: str
    error_context: Optional[Dict[str, Any]]

    # 时间字段
    timestamp: datetime
    created_at: datetime
    reported_at: Optional[datetime]

    # 请求相关信息
    request_url: Optional[str]
    request_method: Optional[str]
    request_params: Optional[Dict[str, Any]]
    response_status: Optional[int]
    response_time: Optional[int]

    # 用户会话信息
    session_id: Optional[str]
    user_ip: Optional[str]
    user_location: Optional[Dict[str, Any]]

    # 应用状态信息
    app_state: Optional[Dict[str, Any]]
    memory_usage: Optional[int]
    battery_level: Optional[int]

    # 兼容字段
    error_stack: Optional[str]
    page_path: Optional[str]
    user_agent: Optional[str]
    device_info: Optional[Dict[str, Any]]
    app_version: Optional[str]
    system_info: Optional[Dict[str, Any]]
    network_type: Optional[str]

    class Config:
        from_attributes = True

class ErrorReportResponse(BaseModel):
    """错误上报响应模式"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")

class ErrorStatsResponse(BaseModel):
    """错误统计响应模式"""
    total_errors: int = Field(..., description="总错误数")
    error_types: Dict[str, int] = Field(..., description="按错误类型统计")
    recent_errors: List[ErrorReportInfo] = Field(..., description="最近错误列表")
    time_range: Dict[str, Any] = Field(..., description="统计时间范围")
