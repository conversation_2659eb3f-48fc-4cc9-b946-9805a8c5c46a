"""
支付相关的Pydantic模式
"""
from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any
from datetime import datetime
from decimal import Decimal
from enum import Enum


class OrderStatusEnum(str, Enum):
    """订单状态枚举"""
    PENDING = "pending"
    PAID = "paid"
    CANCELLED = "cancelled"
    EXPIRED = "expired"
    REFUNDED = "refunded"


class PaymentStatusEnum(str, Enum):
    """支付状态枚举"""
    SUCCESS = "success"
    FAILED = "failed"
    REFUNDED = "refunded"
    PARTIAL_REFUNDED = "partial_refunded"


# ==================== 会员套餐相关 ====================

class MembershipPlanBase(BaseModel):
    """会员套餐基础模式"""
    name: str = Field(..., description="套餐名称", max_length=100)
    description: Optional[str] = Field(None, description="套餐描述")
    price: Decimal = Field(..., description="价格(分)", ge=0)
    original_price: Optional[Decimal] = Field(None, description="原价(分)", ge=0)
    duration_days: int = Field(..., description="有效期(天)", gt=0)
    features: Optional[List[str]] = Field(None, description="套餐特权")
    is_active: bool = Field(True, description="是否启用")
    is_recommended: bool = Field(False, description="是否推荐")
    sort_order: int = Field(0, description="排序")


class MembershipPlanCreate(MembershipPlanBase):
    """创建会员套餐"""
    pass


class MembershipPlanUpdate(BaseModel):
    """更新会员套餐"""
    name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None
    price: Optional[Decimal] = Field(None, ge=0)
    original_price: Optional[Decimal] = Field(None, ge=0)
    duration_days: Optional[int] = Field(None, gt=0)
    features: Optional[List[str]] = None
    is_active: Optional[bool] = None
    is_recommended: Optional[bool] = None
    sort_order: Optional[int] = None


class MembershipPlanInfo(MembershipPlanBase):
    """会员套餐信息"""
    id: int
    created_at: datetime
    updated_at: datetime
    
    # 计算属性
    discount_rate: Optional[float] = Field(None, description="折扣率")
    price_yuan: float = Field(..., description="价格(元)")
    original_price_yuan: Optional[float] = Field(None, description="原价(元)")

    class Config:
        from_attributes = True

    @validator('discount_rate', always=True)
    def calculate_discount_rate(cls, v, values):
        """计算折扣率"""
        if values.get('original_price') and values.get('price'):
            original = float(values['original_price'])
            current = float(values['price'])
            if original > current:
                return round((1 - current / original) * 100, 1)
        return None

    @validator('price_yuan', always=True)
    def calculate_price_yuan(cls, v, values):
        """计算价格(元)"""
        return float(values.get('price', 0)) / 100

    @validator('original_price_yuan', always=True)
    def calculate_original_price_yuan(cls, v, values):
        """计算原价(元)"""
        original_price = values.get('original_price')
        return float(original_price) / 100 if original_price else None


class MembershipPlanListResponse(BaseModel):
    """会员套餐列表响应"""
    plans: List[MembershipPlanInfo]
    total: int


# ==================== 订单相关 ====================

class OrderCreate(BaseModel):
    """创建订单"""
    plan_id: int = Field(..., description="套餐ID")
    client_ip: Optional[str] = Field(None, description="客户端IP")
    user_agent: Optional[str] = Field(None, description="用户代理")


class OrderInfo(BaseModel):
    """订单信息"""
    id: str = Field(..., description="订单号")
    user_id: int = Field(..., description="用户ID")
    plan_id: int = Field(..., description="套餐ID")
    amount: Decimal = Field(..., description="订单金额(分)")
    original_amount: Optional[Decimal] = Field(None, description="原始金额(分)")
    discount_amount: Decimal = Field(..., description="优惠金额(分)")
    status: OrderStatusEnum = Field(..., description="订单状态")
    wx_prepay_id: Optional[str] = Field(None, description="微信预支付ID")
    wx_transaction_id: Optional[str] = Field(None, description="微信交易号")
    paid_at: Optional[datetime] = Field(None, description="支付时间")
    expired_at: datetime = Field(..., description="订单过期时间")
    cancelled_at: Optional[datetime] = Field(None, description="取消时间")
    cancel_reason: Optional[str] = Field(None, description="取消原因")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    # 关联信息
    plan_name: Optional[str] = Field(None, description="套餐名称")
    amount_yuan: float = Field(..., description="订单金额(元)")

    class Config:
        from_attributes = True

    @validator('amount_yuan', always=True)
    def calculate_amount_yuan(cls, v, values):
        """计算订单金额(元)"""
        return float(values.get('amount', 0)) / 100


class OrderListResponse(BaseModel):
    """订单列表响应"""
    orders: List[OrderInfo]
    total: int
    has_more: bool


class OrderCancelRequest(BaseModel):
    """取消订单请求"""
    cancel_reason: Optional[str] = Field(None, description="取消原因", max_length=255)


# ==================== 微信支付相关 ====================

class WeChatPayRequest(BaseModel):
    """微信支付请求"""
    order_id: str = Field(..., description="订单号")


class WeChatPayResponse(BaseModel):
    """微信支付响应"""
    appId: str = Field(..., description="小程序ID")
    timeStamp: str = Field(..., description="时间戳")
    nonceStr: str = Field(..., description="随机字符串")
    package: str = Field(..., description="订单详情扩展字符串")
    signType: str = Field(..., description="签名方式")
    paySign: str = Field(..., description="签名")
    order_id: str = Field(..., description="订单号")


class PaymentCallbackData(BaseModel):
    """支付回调数据"""
    appid: str
    mchid: str
    out_trade_no: str
    transaction_id: str
    trade_type: str
    trade_state: str
    trade_state_desc: str
    bank_type: str
    attach: Optional[str] = None
    success_time: str
    payer: Dict[str, Any]
    amount: Dict[str, Any]


# ==================== 会员状态相关 ====================

class UserMembershipInfo(BaseModel):
    """用户会员信息"""
    id: int
    user_id: int
    plan_id: int
    plan_name: str
    start_date: datetime
    end_date: datetime
    is_active: bool
    auto_renew: bool
    activated_at: datetime
    remaining_days: int

    class Config:
        from_attributes = True

    @validator('remaining_days', always=True)
    def calculate_remaining_days(cls, v, values):
        """计算剩余天数"""
        end_date = values.get('end_date')
        if end_date and isinstance(end_date, datetime):
            remaining = (end_date - datetime.now()).days
            return max(0, remaining)
        return 0


class MembershipStatusResponse(BaseModel):
    """会员状态响应"""
    is_member: bool = Field(..., description="是否会员")
    current_membership: Optional[UserMembershipInfo] = Field(None, description="当前会员信息")
    membership_history: List[UserMembershipInfo] = Field([], description="会员历史记录")


# ==================== 支付记录相关 ====================

class PaymentRecordInfo(BaseModel):
    """支付记录信息"""
    id: int
    order_id: str
    wx_transaction_id: Optional[str]
    payment_method: str
    amount: Decimal
    status: PaymentStatusEnum
    refund_amount: Decimal
    refund_reason: Optional[str]
    refunded_at: Optional[datetime]
    created_at: datetime
    
    amount_yuan: float

    class Config:
        from_attributes = True

    @validator('amount_yuan', always=True)
    def calculate_amount_yuan(cls, v, values):
        """计算支付金额(元)"""
        return float(values.get('amount', 0)) / 100


# ==================== 通用响应 ====================

class PaymentResponse(BaseModel):
    """支付通用响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")


class PaymentStatsResponse(BaseModel):
    """支付统计响应"""
    total_orders: int = Field(..., description="总订单数")
    paid_orders: int = Field(..., description="已支付订单数")
    total_amount: Decimal = Field(..., description="总金额(分)")
    total_amount_yuan: float = Field(..., description="总金额(元)")
    
    @validator('total_amount_yuan', always=True)
    def calculate_total_amount_yuan(cls, v, values):
        """计算总金额(元)"""
        return float(values.get('total_amount', 0)) / 100
