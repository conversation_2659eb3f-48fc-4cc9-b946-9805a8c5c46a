"""
免费简历模板相关的数据模式
"""
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

class FreeTemplateBase(BaseModel):
    """免费模板基础信息"""
    id: str = Field(..., description="模板ID，如：blackWhite/10.jpg")
    batch_flag: str = Field(..., description="批次标识，如：blackWhite")
    thumb_path: str = Field(..., description="缩略图路径")
    baidu_url: Optional[str] = Field(None, description="百度网盘链接")
    baidu_pass: Optional[str] = Field(None, description="百度网盘提取码")
    quark_url: Optional[str] = Field(None, description="夸克网盘链接")
    quark_pass: Optional[str] = Field(None, description="夸克网盘提取码")
    download_count: int = Field(default=0, description="下载次数")
    type: str = Field(default="word", description="文件类型")

class FreeTemplateCreate(FreeTemplateBase):
    """创建免费模板"""
    pass

class FreeTemplateUpdate(BaseModel):
    """更新免费模板"""
    batch_flag: Optional[str] = None
    thumb_path: Optional[str] = None
    baidu_url: Optional[str] = None
    baidu_pass: Optional[str] = None
    quark_url: Optional[str] = None
    quark_pass: Optional[str] = None
    download_count: Optional[int] = None
    type: Optional[str] = None

class FreeTemplateInfo(FreeTemplateBase):
    """免费模板信息响应（完整信息，用于管理后台）"""
    thumb_url: Optional[str] = Field(None, description="缩略图完整URL")
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class FreeTemplateWechatInfo(BaseModel):
    """免费模板信息响应（微信端精简版）"""
    id: str = Field(..., description="模板ID")
    thumb_url: Optional[str] = Field(None, description="缩略图完整URL")
    baidu_url: Optional[str] = Field(None, description="百度网盘链接")
    baidu_pass: Optional[str] = Field(None, description="百度网盘提取码")
    quark_url: Optional[str] = Field(None, description="夸克网盘链接")
    quark_pass: Optional[str] = Field(None, description="夸克网盘提取码")
    type: str = Field(default="word", description="文件类型")

class FreeTemplateListResponse(BaseModel):
    """免费模板列表响应（微信端精简版）"""
    total: int = Field(..., description="总数量")
    templates: List[FreeTemplateWechatInfo] = Field(..., description="模板列表")

class FreeTemplateDownloadResponse(BaseModel):
    """免费模板下载链接响应"""
    id: str = Field(..., description="模板ID")
    baidu_url: Optional[str] = Field(None, description="百度网盘链接")
    baidu_pass: Optional[str] = Field(None, description="百度网盘提取码")
    quark_url: Optional[str] = Field(None, description="夸克网盘链接")
    quark_pass: Optional[str] = Field(None, description="夸克网盘提取码")
    download_count: int = Field(..., description="下载次数")
    message: str = Field(..., description="响应消息")

class BatchTemplatesResponse(BaseModel):
    """批次模板响应（微信端精简版）"""
    batch_flag: str = Field(..., description="批次标识")
    templates: List[FreeTemplateWechatInfo] = Field(..., description="该批次的模板列表")
    total: int = Field(..., description="该批次模板总数")

class FreeTemplateStyleInfo(BaseModel):
    """简历样式模板信息（微信端超精简版）"""
    id: str = Field(..., description="模板ID")
    thumb_url: Optional[str] = Field(None, description="缩略图完整URL")

class FreeTemplateStyleListResponse(BaseModel):
    """简历样式模板列表响应"""
    code: int = Field(200, description="响应状态码")
    message: str = Field("获取成功", description="响应消息")
    data: dict = Field(..., description="响应数据")

    class Config:
        schema_extra = {
            "example": {
                "code": 200,
                "message": "获取成功",
                "data": {
                    "templates": [
                        {
                            "id": "style_001",
                            "thumb_url": "https://example.com/thumbnails/style_001.jpg"
                        }
                    ],
                    "total": 45,
                    "skip": 0,
                    "limit": 20,
                    "has_more": True
                }
            }
        }
