{"deploy_time": "2025-07-03T21:15:17.919089", "steps": [{"timestamp": "2025-07-03T21:15:08.034071", "step": "检查Python版本", "status": "success", "message": "Python版本: 3.13.2 (main, Jul  3 2025, 02:32:13) [GCC 12.3.1 20230912 (OpenCloudOS 12.3.1.3-1)]"}, {"timestamp": "2025-07-03T21:15:08.091571", "step": "检查数据库连接", "status": "success", "message": "连接正常"}, {"timestamp": "2025-07-03T21:15:08.091713", "step": "检查脚本 migrate_complete_database.py", "status": "success", "message": "文件存在"}, {"timestamp": "2025-07-03T21:15:08.091765", "step": "检查脚本 export_data_backup.py", "status": "success", "message": "文件存在"}, {"timestamp": "2025-07-03T21:15:08.091814", "step": "检查脚本 import_data_backup.py", "status": "success", "message": "文件存在"}, {"timestamp": "2025-07-03T21:15:08.093727", "step": "更新配置 migrate_complete_database.py", "status": "success", "message": "配置已更新"}, {"timestamp": "2025-07-03T21:15:08.095010", "step": "更新配置 export_data_backup.py", "status": "success", "message": "配置已更新"}, {"timestamp": "2025-07-03T21:15:08.096040", "step": "更新配置 import_data_backup.py", "status": "success", "message": "配置已更新"}, {"timestamp": "2025-07-03T21:15:16.037799", "step": "创建数据库结构", "status": "success", "message": "所有表创建完成"}, {"timestamp": "2025-07-03T21:15:17.062235", "step": "导入 free_templates", "status": "success", "message": "从 free_templates_20250703_112944.json 导入成功"}, {"timestamp": "2025-07-03T21:15:17.667560", "step": "导入 resume_thumbs", "status": "success", "message": "从 resume_thumbs_20250703_112944.json 导入成功"}, {"timestamp": "2025-07-03T21:15:17.667837", "step": "导入生产环境数据", "status": "success", "message": "成功导入: free_templates, resume_thumbs"}, {"timestamp": "2025-07-03T21:15:17.845194", "step": "验证表结构", "status": "success", "message": "所有表都存在"}, {"timestamp": "2025-07-03T21:15:17.897082", "step": "验证 free_templates 数据", "status": "success", "message": "113 条记录"}, {"timestamp": "2025-07-03T21:15:17.918458", "step": "验证 resume_thumbs 数据", "status": "success", "message": "6 条记录"}]}