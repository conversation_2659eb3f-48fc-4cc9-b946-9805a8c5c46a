#!/usr/bin/env python3
"""
数据库验证脚本
用于验证迁移结果和数据库完整性
"""
import mysql.connector
from mysql.connector import Error
import logging
import sys
import json
import argparse
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库连接配置
DB_CONFIG = {
    'host': 'localhost',
    'database': 'resume_service',
    'user': 'resume_user',
    'password': 'Resume123!'
}

class DatabaseVerifier:
    def __init__(self, config):
        self.config = config
        self.verification_results = []
    
    def get_connection(self):
        """获取数据库连接"""
        try:
            connection = mysql.connector.connect(**self.config)
            return connection
        except Error as e:
            logger.error(f"数据库连接失败: {e}")
            return None
    
    def log_result(self, test_name, status, message="", details=None):
        """记录验证结果"""
        result = {
            'test_name': test_name,
            'status': status,
            'message': message,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.verification_results.append(result)
        
        status_symbol = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        logger.info(f"{status_symbol} {test_name}: {message}")
    
    def verify_database_connection(self):
        """验证数据库连接"""
        connection = self.get_connection()
        if connection:
            try:
                cursor = connection.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                cursor.close()
                connection.close()
                self.log_result("数据库连接", "PASS", "连接正常")
                return True
            except Error as e:
                self.log_result("数据库连接", "FAIL", f"查询失败: {e}")
                return False
        else:
            self.log_result("数据库连接", "FAIL", "无法建立连接")
            return False
    
    def verify_table_structure(self):
        """验证表结构"""
        connection = self.get_connection()
        if not connection:
            return False
        
        try:
            cursor = connection.cursor()
            
            # 期望的表结构
            expected_tables = {
                'users': ['id', 'openid', 'nickname', 'is_member', 'created_at'],
                'user_actions': ['id', 'user_id', 'action_type', 'created_at'],
                'feedback': ['id', 'user_id', 'content', 'status', 'created_at'],
                'feedback_replies': ['id', 'feedback_id', 'admin_name', 'reply_content'],
                'free_templates': ['id', 'batch_flag', 'thumb_path', 'download_count'],
                'resume_thumbs': ['id', 'batch_flag', 'thumb_path', 'sort_index'],
                'error_reports': ['id', 'error_type', 'error_message', 'created_at']
            }
            
            # 检查所有表是否存在
            cursor.execute(f"SHOW TABLES FROM {self.config['database']}")
            existing_tables = [table[0] for table in cursor.fetchall()]
            
            missing_tables = []
            for table_name in expected_tables.keys():
                if table_name not in existing_tables:
                    missing_tables.append(table_name)
            
            if missing_tables:
                self.log_result("表结构检查", "FAIL", f"缺少表: {', '.join(missing_tables)}")
                return False
            
            # 检查每个表的关键字段
            table_issues = []
            for table_name, required_columns in expected_tables.items():
                cursor.execute(f"DESCRIBE {table_name}")
                existing_columns = [col[0] for col in cursor.fetchall()]
                
                missing_columns = [col for col in required_columns if col not in existing_columns]
                if missing_columns:
                    table_issues.append(f"{table_name}: 缺少字段 {', '.join(missing_columns)}")
            
            if table_issues:
                self.log_result("表结构检查", "FAIL", "字段缺失", table_issues)
                return False
            
            self.log_result("表结构检查", "PASS", f"所有 {len(expected_tables)} 个表结构正常")
            return True
            
        except Error as e:
            self.log_result("表结构检查", "FAIL", str(e))
            return False
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()
    
    def verify_indexes(self):
        """验证索引"""
        connection = self.get_connection()
        if not connection:
            return False
        
        try:
            cursor = connection.cursor()
            
            # 关键索引检查
            critical_indexes = [
                ('users', 'openid'),
                ('user_actions', 'user_id'),
                ('feedback', 'user_id'),
                ('free_templates', 'batch_flag'),
                ('resume_thumbs', 'batch_flag'),
                ('error_reports', 'error_type')
            ]
            
            missing_indexes = []
            for table_name, column_name in critical_indexes:
                cursor.execute(f"SHOW INDEX FROM {table_name} WHERE Column_name = '{column_name}'")
                indexes = cursor.fetchall()
                
                if not indexes:
                    missing_indexes.append(f"{table_name}.{column_name}")
            
            if missing_indexes:
                self.log_result("索引检查", "WARN", f"缺少索引: {', '.join(missing_indexes)}")
            else:
                self.log_result("索引检查", "PASS", "关键索引都存在")
            
            return True
            
        except Error as e:
            self.log_result("索引检查", "FAIL", str(e))
            return False
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()
    
    def verify_data_integrity(self):
        """验证数据完整性"""
        connection = self.get_connection()
        if not connection:
            return False
        
        try:
            cursor = connection.cursor()
            
            # 检查各表的记录数
            table_counts = {}
            tables_to_check = ['users', 'user_actions', 'feedback', 'free_templates', 'resume_thumbs', 'error_reports']
            
            for table_name in tables_to_check:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                table_counts[table_name] = count
            
            # 检查关键表是否有数据
            if table_counts.get('free_templates', 0) == 0:
                self.log_result("数据完整性", "WARN", "free_templates表没有数据")
            
            if table_counts.get('resume_thumbs', 0) == 0:
                self.log_result("数据完整性", "WARN", "resume_thumbs表没有数据")
            
            # 检查数据一致性
            consistency_issues = []
            
            # 检查用户行为记录中的user_id是否都存在于users表
            cursor.execute("""
                SELECT COUNT(*) FROM user_actions ua 
                LEFT JOIN users u ON ua.user_id = u.id 
                WHERE u.id IS NULL
            """)
            orphaned_actions = cursor.fetchone()[0]
            if orphaned_actions > 0:
                consistency_issues.append(f"user_actions表中有 {orphaned_actions} 条记录的user_id不存在")
            
            # 检查反馈记录中的user_id是否都存在于users表
            cursor.execute("""
                SELECT COUNT(*) FROM feedback f 
                LEFT JOIN users u ON f.user_id = u.id 
                WHERE u.id IS NULL
            """)
            orphaned_feedback = cursor.fetchone()[0]
            if orphaned_feedback > 0:
                consistency_issues.append(f"feedback表中有 {orphaned_feedback} 条记录的user_id不存在")
            
            if consistency_issues:
                self.log_result("数据完整性", "WARN", "发现数据一致性问题", consistency_issues)
            else:
                self.log_result("数据完整性", "PASS", "数据一致性检查通过")
            
            # 记录表统计信息
            self.log_result("表统计信息", "INFO", "记录数统计", table_counts)
            
            return True
            
        except Error as e:
            self.log_result("数据完整性", "FAIL", str(e))
            return False
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()
    
    def verify_permissions(self):
        """验证数据库权限"""
        connection = self.get_connection()
        if not connection:
            return False
        
        try:
            cursor = connection.cursor()
            
            # 测试基本操作权限
            test_operations = []
            
            # 测试SELECT权限
            try:
                cursor.execute("SELECT 1 FROM users LIMIT 1")
                test_operations.append(("SELECT", "PASS"))
            except Error:
                test_operations.append(("SELECT", "FAIL"))
            
            # 测试INSERT权限（使用临时表）
            try:
                cursor.execute("CREATE TEMPORARY TABLE temp_test (id INT)")
                cursor.execute("INSERT INTO temp_test VALUES (1)")
                cursor.execute("DROP TEMPORARY TABLE temp_test")
                test_operations.append(("INSERT", "PASS"))
            except Error:
                test_operations.append(("INSERT", "FAIL"))
            
            # 测试UPDATE权限
            try:
                cursor.execute("UPDATE users SET updated_at = updated_at WHERE 1=0")
                test_operations.append(("UPDATE", "PASS"))
            except Error:
                test_operations.append(("UPDATE", "FAIL"))
            
            failed_operations = [op for op, status in test_operations if status == "FAIL"]
            if failed_operations:
                self.log_result("权限检查", "FAIL", f"缺少权限: {', '.join(failed_operations)}")
                return False
            else:
                self.log_result("权限检查", "PASS", "所有必要权限都具备")
                return True
            
        except Error as e:
            self.log_result("权限检查", "FAIL", str(e))
            return False
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()
    
    def verify_performance(self):
        """验证数据库性能"""
        connection = self.get_connection()
        if not connection:
            return False
        
        try:
            cursor = connection.cursor()
            
            # 测试查询性能
            import time
            
            # 测试简单查询
            start_time = time.time()
            cursor.execute("SELECT COUNT(*) FROM users")
            cursor.fetchone()
            simple_query_time = time.time() - start_time
            
            # 测试复杂查询
            start_time = time.time()
            cursor.execute("""
                SELECT u.id, COUNT(ua.id) as action_count 
                FROM users u 
                LEFT JOIN user_actions ua ON u.id = ua.user_id 
                GROUP BY u.id 
                LIMIT 10
            """)
            cursor.fetchall()
            complex_query_time = time.time() - start_time
            
            performance_info = {
                'simple_query_ms': round(simple_query_time * 1000, 2),
                'complex_query_ms': round(complex_query_time * 1000, 2)
            }
            
            # 性能警告阈值
            if simple_query_time > 1.0:
                self.log_result("性能检查", "WARN", f"简单查询较慢: {performance_info['simple_query_ms']}ms")
            elif complex_query_time > 5.0:
                self.log_result("性能检查", "WARN", f"复杂查询较慢: {performance_info['complex_query_ms']}ms")
            else:
                self.log_result("性能检查", "PASS", "查询性能正常", performance_info)
            
            return True
            
        except Error as e:
            self.log_result("性能检查", "FAIL", str(e))
            return False
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()
    
    def run_all_verifications(self):
        """运行所有验证测试"""
        logger.info("=" * 60)
        logger.info("开始数据库验证")
        logger.info("=" * 60)
        
        verifications = [
            ("数据库连接", self.verify_database_connection),
            ("表结构", self.verify_table_structure),
            ("索引", self.verify_indexes),
            ("数据完整性", self.verify_data_integrity),
            ("权限", self.verify_permissions),
            ("性能", self.verify_performance)
        ]
        
        passed = 0
        failed = 0
        warnings = 0
        
        for name, verification_func in verifications:
            try:
                verification_func()
            except Exception as e:
                self.log_result(name, "FAIL", f"验证异常: {e}")
        
        # 统计结果
        for result in self.verification_results:
            if result['status'] == 'PASS':
                passed += 1
            elif result['status'] == 'FAIL':
                failed += 1
            elif result['status'] == 'WARN':
                warnings += 1
        
        logger.info("=" * 60)
        logger.info("验证完成")
        logger.info(f"通过: {passed}, 失败: {failed}, 警告: {warnings}")
        logger.info("=" * 60)
        
        return failed == 0
    
    def save_verification_report(self, output_file=None):
        """保存验证报告"""
        if not output_file:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"verification_report_{timestamp}.json"
        
        report = {
            'verification_time': datetime.now().isoformat(),
            'database_config': {
                'host': self.config['host'],
                'database': self.config['database'],
                'user': self.config['user']
            },
            'results': self.verification_results
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"验证报告已保存: {output_file}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='数据库验证脚本')
    parser.add_argument('--host', default='localhost', help='数据库主机')
    parser.add_argument('--database', default='resume_service', help='数据库名称')
    parser.add_argument('--user', default='resume_user', help='数据库用户')
    parser.add_argument('--password', default='Resume123!', help='数据库密码')
    parser.add_argument('--output', help='验证报告输出文件')
    
    args = parser.parse_args()
    
    # 构建数据库配置
    config = {
        'host': args.host,
        'database': args.database,
        'user': args.user,
        'password': args.password
    }
    
    # 创建验证器
    verifier = DatabaseVerifier(config)
    
    try:
        # 运行所有验证
        success = verifier.run_all_verifications()
        
        # 保存验证报告
        verifier.save_verification_report(args.output)
        
        if success:
            logger.info("所有验证通过！")
            sys.exit(0)
        else:
            logger.error("验证失败！")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("验证被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.exception("验证过程中发生异常")
        sys.exit(1)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
数据库验证脚本
用于验证迁移结果和数据库完整性
"""
import mysql.connector
from mysql.connector import Error
import logging
import sys
import json
import argparse
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库连接配置
DB_CONFIG = {
    'host': 'localhost',
    'database': 'resume_service',
    'user': 'resume_user',
    'password': 'Resume123!'
}

class DatabaseVerifier:
    def __init__(self, config):
        self.config = config
        self.verification_results = []
    
    def get_connection(self):
        """获取数据库连接"""
        try:
            connection = mysql.connector.connect(**self.config)
            return connection
        except Error as e:
            logger.error(f"数据库连接失败: {e}")
            return None
    
    def log_result(self, test_name, status, message="", details=None):
        """记录验证结果"""
        result = {
            'test_name': test_name,
            'status': status,
            'message': message,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.verification_results.append(result)
        
        status_symbol = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        logger.info(f"{status_symbol} {test_name}: {message}")
    
    def verify_database_connection(self):
        """验证数据库连接"""
        connection = self.get_connection()
        if connection:
            try:
                cursor = connection.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                cursor.close()
                connection.close()
                self.log_result("数据库连接", "PASS", "连接正常")
                return True
            except Error as e:
                self.log_result("数据库连接", "FAIL", f"查询失败: {e}")
                return False
        else:
            self.log_result("数据库连接", "FAIL", "无法建立连接")
            return False
    
    def verify_table_structure(self):
        """验证表结构"""
        connection = self.get_connection()
        if not connection:
            return False
        
        try:
            cursor = connection.cursor()
            
            # 期望的表结构
            expected_tables = {
                'users': ['id', 'openid', 'nickname', 'is_member', 'created_at'],
                'user_actions': ['id', 'user_id', 'action_type', 'created_at'],
                'feedback': ['id', 'user_id', 'content', 'status', 'created_at'],
                'feedback_replies': ['id', 'feedback_id', 'admin_name', 'reply_content'],
                'free_templates': ['id', 'batch_flag', 'thumb_path', 'download_count'],
                'resume_thumbs': ['id', 'batch_flag', 'thumb_path', 'sort_index'],
                'error_reports': ['id', 'error_type', 'error_message', 'created_at']
            }
            
            # 检查所有表是否存在
            cursor.execute(f"SHOW TABLES FROM {self.config['database']}")
            existing_tables = [table[0] for table in cursor.fetchall()]
            
            missing_tables = []
            for table_name in expected_tables.keys():
                if table_name not in existing_tables:
                    missing_tables.append(table_name)
            
            if missing_tables:
                self.log_result("表结构检查", "FAIL", f"缺少表: {', '.join(missing_tables)}")
                return False
            
            # 检查每个表的关键字段
            table_issues = []
            for table_name, required_columns in expected_tables.items():
                cursor.execute(f"DESCRIBE {table_name}")
                existing_columns = [col[0] for col in cursor.fetchall()]
                
                missing_columns = [col for col in required_columns if col not in existing_columns]
                if missing_columns:
                    table_issues.append(f"{table_name}: 缺少字段 {', '.join(missing_columns)}")
            
            if table_issues:
                self.log_result("表结构检查", "FAIL", "字段缺失", table_issues)
                return False
            
            self.log_result("表结构检查", "PASS", f"所有 {len(expected_tables)} 个表结构正常")
            return True
            
        except Error as e:
            self.log_result("表结构检查", "FAIL", str(e))
            return False
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()
    
    def verify_indexes(self):
        """验证索引"""
        connection = self.get_connection()
        if not connection:
            return False
        
        try:
            cursor = connection.cursor()
            
            # 关键索引检查
            critical_indexes = [
                ('users', 'openid'),
                ('user_actions', 'user_id'),
                ('feedback', 'user_id'),
                ('free_templates', 'batch_flag'),
                ('resume_thumbs', 'batch_flag'),
                ('error_reports', 'error_type')
            ]
            
            missing_indexes = []
            for table_name, column_name in critical_indexes:
                cursor.execute(f"SHOW INDEX FROM {table_name} WHERE Column_name = '{column_name}'")
                indexes = cursor.fetchall()
                
                if not indexes:
                    missing_indexes.append(f"{table_name}.{column_name}")
            
            if missing_indexes:
                self.log_result("索引检查", "WARN", f"缺少索引: {', '.join(missing_indexes)}")
            else:
                self.log_result("索引检查", "PASS", "关键索引都存在")
            
            return True
            
        except Error as e:
            self.log_result("索引检查", "FAIL", str(e))
            return False
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()
    
    def verify_data_integrity(self):
        """验证数据完整性"""
        connection = self.get_connection()
        if not connection:
            return False
        
        try:
            cursor = connection.cursor()
            
            # 检查各表的记录数
            table_counts = {}
            tables_to_check = ['users', 'user_actions', 'feedback', 'free_templates', 'resume_thumbs', 'error_reports']
            
            for table_name in tables_to_check:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                table_counts[table_name] = count
            
            # 检查关键表是否有数据
            if table_counts.get('free_templates', 0) == 0:
                self.log_result("数据完整性", "WARN", "free_templates表没有数据")
            
            if table_counts.get('resume_thumbs', 0) == 0:
                self.log_result("数据完整性", "WARN", "resume_thumbs表没有数据")
            
            # 检查数据一致性
            consistency_issues = []
            
            # 检查用户行为记录中的user_id是否都存在于users表
            cursor.execute("""
                SELECT COUNT(*) FROM user_actions ua 
                LEFT JOIN users u ON ua.user_id = u.id 
                WHERE u.id IS NULL
            """)
            orphaned_actions = cursor.fetchone()[0]
            if orphaned_actions > 0:
                consistency_issues.append(f"user_actions表中有 {orphaned_actions} 条记录的user_id不存在")
            
            # 检查反馈记录中的user_id是否都存在于users表
            cursor.execute("""
                SELECT COUNT(*) FROM feedback f 
                LEFT JOIN users u ON f.user_id = u.id 
                WHERE u.id IS NULL
            """)
            orphaned_feedback = cursor.fetchone()[0]
            if orphaned_feedback > 0:
                consistency_issues.append(f"feedback表中有 {orphaned_feedback} 条记录的user_id不存在")
            
            if consistency_issues:
                self.log_result("数据完整性", "WARN", "发现数据一致性问题", consistency_issues)
            else:
                self.log_result("数据完整性", "PASS", "数据一致性检查通过")
            
            # 记录表统计信息
            self.log_result("表统计信息", "INFO", "记录数统计", table_counts)
            
            return True
            
        except Error as e:
            self.log_result("数据完整性", "FAIL", str(e))
            return False
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()
    
    def verify_permissions(self):
        """验证数据库权限"""
        connection = self.get_connection()
        if not connection:
            return False
        
        try:
            cursor = connection.cursor()
            
            # 测试基本操作权限
            test_operations = []
            
            # 测试SELECT权限
            try:
                cursor.execute("SELECT 1 FROM users LIMIT 1")
                test_operations.append(("SELECT", "PASS"))
            except Error:
                test_operations.append(("SELECT", "FAIL"))
            
            # 测试INSERT权限（使用临时表）
            try:
                cursor.execute("CREATE TEMPORARY TABLE temp_test (id INT)")
                cursor.execute("INSERT INTO temp_test VALUES (1)")
                cursor.execute("DROP TEMPORARY TABLE temp_test")
                test_operations.append(("INSERT", "PASS"))
            except Error:
                test_operations.append(("INSERT", "FAIL"))
            
            # 测试UPDATE权限
            try:
                cursor.execute("UPDATE users SET updated_at = updated_at WHERE 1=0")
                test_operations.append(("UPDATE", "PASS"))
            except Error:
                test_operations.append(("UPDATE", "FAIL"))
            
            failed_operations = [op for op, status in test_operations if status == "FAIL"]
            if failed_operations:
                self.log_result("权限检查", "FAIL", f"缺少权限: {', '.join(failed_operations)}")
                return False
            else:
                self.log_result("权限检查", "PASS", "所有必要权限都具备")
                return True
            
        except Error as e:
            self.log_result("权限检查", "FAIL", str(e))
            return False
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()
    
    def verify_performance(self):
        """验证数据库性能"""
        connection = self.get_connection()
        if not connection:
            return False
        
        try:
            cursor = connection.cursor()
            
            # 测试查询性能
            import time
            
            # 测试简单查询
            start_time = time.time()
            cursor.execute("SELECT COUNT(*) FROM users")
            cursor.fetchone()
            simple_query_time = time.time() - start_time
            
            # 测试复杂查询
            start_time = time.time()
            cursor.execute("""
                SELECT u.id, COUNT(ua.id) as action_count 
                FROM users u 
                LEFT JOIN user_actions ua ON u.id = ua.user_id 
                GROUP BY u.id 
                LIMIT 10
            """)
            cursor.fetchall()
            complex_query_time = time.time() - start_time
            
            performance_info = {
                'simple_query_ms': round(simple_query_time * 1000, 2),
                'complex_query_ms': round(complex_query_time * 1000, 2)
            }
            
            # 性能警告阈值
            if simple_query_time > 1.0:
                self.log_result("性能检查", "WARN", f"简单查询较慢: {performance_info['simple_query_ms']}ms")
            elif complex_query_time > 5.0:
                self.log_result("性能检查", "WARN", f"复杂查询较慢: {performance_info['complex_query_ms']}ms")
            else:
                self.log_result("性能检查", "PASS", "查询性能正常", performance_info)
            
            return True
            
        except Error as e:
            self.log_result("性能检查", "FAIL", str(e))
            return False
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()
    
    def run_all_verifications(self):
        """运行所有验证测试"""
        logger.info("=" * 60)
        logger.info("开始数据库验证")
        logger.info("=" * 60)
        
        verifications = [
            ("数据库连接", self.verify_database_connection),
            ("表结构", self.verify_table_structure),
            ("索引", self.verify_indexes),
            ("数据完整性", self.verify_data_integrity),
            ("权限", self.verify_permissions),
            ("性能", self.verify_performance)
        ]
        
        passed = 0
        failed = 0
        warnings = 0
        
        for name, verification_func in verifications:
            try:
                verification_func()
            except Exception as e:
                self.log_result(name, "FAIL", f"验证异常: {e}")
        
        # 统计结果
        for result in self.verification_results:
            if result['status'] == 'PASS':
                passed += 1
            elif result['status'] == 'FAIL':
                failed += 1
            elif result['status'] == 'WARN':
                warnings += 1
        
        logger.info("=" * 60)
        logger.info("验证完成")
        logger.info(f"通过: {passed}, 失败: {failed}, 警告: {warnings}")
        logger.info("=" * 60)
        
        return failed == 0
    
    def save_verification_report(self, output_file=None):
        """保存验证报告"""
        if not output_file:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"verification_report_{timestamp}.json"
        
        report = {
            'verification_time': datetime.now().isoformat(),
            'database_config': {
                'host': self.config['host'],
                'database': self.config['database'],
                'user': self.config['user']
            },
            'results': self.verification_results
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"验证报告已保存: {output_file}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='数据库验证脚本')
    parser.add_argument('--host', default='localhost', help='数据库主机')
    parser.add_argument('--database', default='resume_service', help='数据库名称')
    parser.add_argument('--user', default='resume_user', help='数据库用户')
    parser.add_argument('--password', default='Resume123!', help='数据库密码')
    parser.add_argument('--output', help='验证报告输出文件')
    
    args = parser.parse_args()
    
    # 构建数据库配置
    config = {
        'host': args.host,
        'database': args.database,
        'user': args.user,
        'password': args.password
    }
    
    # 创建验证器
    verifier = DatabaseVerifier(config)
    
    try:
        # 运行所有验证
        success = verifier.run_all_verifications()
        
        # 保存验证报告
        verifier.save_verification_report(args.output)
        
        if success:
            logger.info("所有验证通过！")
            sys.exit(0)
        else:
            logger.error("验证失败！")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("验证被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.exception("验证过程中发生异常")
        sys.exit(1)

if __name__ == "__main__":
    main()
