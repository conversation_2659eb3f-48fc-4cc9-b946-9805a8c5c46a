# 免费简历模板Excel导入功能使用说明

## 概述

本文档介绍如何使用增强版的免费简历模板导入脚本，该脚本现在支持Excel文件导入，并可以指定特定的工作表进行导入。

## 新增功能

### 1. Excel文件支持
- 支持 `.xlsx` 和 `.xls` 格式的Excel文件
- 自动检测文件格式（CSV/Excel）
- 支持多工作表Excel文件

### 2. 工作表选择
- 可以指定要导入的工作表名称
- 如果不指定，默认使用第一个工作表
- 提供工作表列表查看功能

### 3. 增强的字段映射
- 支持更多中文列名格式
- 自动处理Excel中的数据类型转换
- 更好的错误处理和日志记录

## 使用方法

### 基本命令

```bash
# 查看帮助信息
python server_scripts/import_free_templates.py --help

# 从CSV文件导入（保持向后兼容）
python server_scripts/import_free_templates.py data.csv

# 从Excel文件导入（使用第一个工作表）
python server_scripts/import_free_templates.py data.xlsx

# 从Excel文件的指定工作表导入
python server_scripts/import_free_templates.py data.xlsx --sheet "工作表名称"
```

### 辅助功能

```bash
# 创建示例CSV文件
python server_scripts/import_free_templates.py --sample-csv

# 创建示例Excel文件（包含多个工作表）
python server_scripts/import_free_templates.py --sample-excel

# 列出Excel文件中的所有工作表
python server_scripts/import_free_templates.py --list-sheets data.xlsx
```

## 支持的列名格式

脚本支持多种列名格式，包括中英文：

| 数据库字段 | 支持的列名 |
|-----------|-----------|
| id | id, ID, 模板ID, template_id, 编号 |
| batch_flag | batch_flag, batchFlag, 批次, batch, 分类, 批次标识 |
| thumb_path | thumb_path, thumbPath, 缩略图路径, thumbnail, 图片路径, 缩略图 |
| baidu_url | baidu_url, baiduUrl, 百度链接, baidu_link, 百度网盘, 百度网盘链接 |
| baidu_pass | baidu_pass, baiduPass, 百度提取码, baidu_password, 百度密码, 百度网盘提取码 |
| quark_url | quark_url, quarkUrl, 夸克链接, quark_link, 夸克网盘, 夸克网盘链接 |
| quark_pass | quark_pass, quarkPass, 夸克提取码, quark_password, 夸克密码, 夸克网盘提取码 |
| download_count | download_count, downloadCount, 下载次数, downloads, 下载量 |
| type | type, file_type, 文件类型, 类型, 格式 |

## 使用示例

### 1. 创建示例Excel文件

```bash
python server_scripts/import_free_templates.py --sample-excel
```

这将创建一个名为 `sample_free_templates.xlsx` 的示例文件，包含以下工作表：
- 免费模板（所有数据）
- 黑白模板（blackWhite分类）
- 彩色模板（colorful分类）
- 现代模板（modern分类）

### 2. 查看工作表列表

```bash
python server_scripts/import_free_templates.py --list-sheets sample_free_templates.xlsx
```

输出示例：
```
Excel文件 'sample_free_templates.xlsx' 包含的工作表:
  1. 免费模板
  2. 黑白模板
  3. 彩色模板
  4. 现代模板
```

### 3. 导入整个Excel文件

```bash
python server_scripts/import_free_templates.py sample_free_templates.xlsx
```

### 4. 导入指定工作表

```bash
python server_scripts/import_free_templates.py sample_free_templates.xlsx --sheet "黑白模板"
```

## 数据格式要求

### 必需字段
- **id**: 模板ID，必须唯一，建议格式为 `分类/文件名.扩展名`

### 可选字段
- **batch_flag**: 批次标识，如果不提供，会从id中自动提取
- **thumb_path**: 缩略图路径，如果不提供，会自动生成
- **baidu_url**: 百度网盘链接
- **baidu_pass**: 百度网盘提取码
- **quark_url**: 夸克网盘链接
- **quark_pass**: 夸克网盘提取码
- **download_count**: 下载次数，默认为0
- **type**: 文件类型，默认为"word"

## 错误处理

脚本具有完善的错误处理机制：

1. **文件不存在**: 会提示文件路径错误
2. **工作表不存在**: 会列出可用的工作表名称
3. **数据格式错误**: 会跳过错误行并继续处理
4. **数据库连接错误**: 会回滚事务并提示错误信息

## 导入结果

导入完成后，脚本会显示详细的统计信息：

```
导入完成！
新增模板: 3 个
更新模板: 1 个
错误记录: 0 个
总计处理: 4 个模板
```

## 注意事项

1. **数据备份**: 导入前建议备份数据库
2. **重复数据**: 相同ID的记录会被更新而不是重复插入
3. **编码格式**: Excel文件请使用UTF-8编码保存
4. **工作表名称**: 工作表名称区分大小写
5. **依赖安装**: 确保已安装pandas和openpyxl库

## 依赖要求

```bash
pip install pandas>=2.0.0 openpyxl>=3.1.0
```

## 故障排除

### 常见问题

1. **ModuleNotFoundError**: 确保已安装所需依赖
2. **数据库连接失败**: 检查数据库配置和连接
3. **Excel文件损坏**: 尝试重新保存Excel文件
4. **权限问题**: 确保脚本有读取文件的权限

### 调试模式

脚本会显示详细的SQL执行日志，有助于调试数据库相关问题。

## 更新日志

- **v2.0**: 新增Excel文件支持和工作表选择功能
- **v1.0**: 基础CSV导入功能

# 免费简历模板Excel导入功能使用说明

## 概述

本文档介绍如何使用增强版的免费简历模板导入脚本，该脚本现在支持Excel文件导入，并可以指定特定的工作表进行导入。

## 新增功能

### 1. Excel文件支持
- 支持 `.xlsx` 和 `.xls` 格式的Excel文件
- 自动检测文件格式（CSV/Excel）
- 支持多工作表Excel文件

### 2. 工作表选择
- 可以指定要导入的工作表名称
- 如果不指定，默认使用第一个工作表
- 提供工作表列表查看功能

### 3. 增强的字段映射
- 支持更多中文列名格式
- 自动处理Excel中的数据类型转换
- 更好的错误处理和日志记录

## 使用方法

### 基本命令

```bash
# 查看帮助信息
python server_scripts/import_free_templates.py --help

# 从CSV文件导入（保持向后兼容）
python server_scripts/import_free_templates.py data.csv

# 从Excel文件导入（使用第一个工作表）
python server_scripts/import_free_templates.py data.xlsx

# 从Excel文件的指定工作表导入
python server_scripts/import_free_templates.py data.xlsx --sheet "工作表名称"
```

### 辅助功能

```bash
# 创建示例CSV文件
python server_scripts/import_free_templates.py --sample-csv

# 创建示例Excel文件（包含多个工作表）
python server_scripts/import_free_templates.py --sample-excel

# 列出Excel文件中的所有工作表
python server_scripts/import_free_templates.py --list-sheets data.xlsx
```

## 支持的列名格式

脚本支持多种列名格式，包括中英文：

| 数据库字段 | 支持的列名 |
|-----------|-----------|
| id | id, ID, 模板ID, template_id, 编号 |
| batch_flag | batch_flag, batchFlag, 批次, batch, 分类, 批次标识 |
| thumb_path | thumb_path, thumbPath, 缩略图路径, thumbnail, 图片路径, 缩略图 |
| baidu_url | baidu_url, baiduUrl, 百度链接, baidu_link, 百度网盘, 百度网盘链接 |
| baidu_pass | baidu_pass, baiduPass, 百度提取码, baidu_password, 百度密码, 百度网盘提取码 |
| quark_url | quark_url, quarkUrl, 夸克链接, quark_link, 夸克网盘, 夸克网盘链接 |
| quark_pass | quark_pass, quarkPass, 夸克提取码, quark_password, 夸克密码, 夸克网盘提取码 |
| download_count | download_count, downloadCount, 下载次数, downloads, 下载量 |
| type | type, file_type, 文件类型, 类型, 格式 |

## 使用示例

### 1. 创建示例Excel文件

```bash
python server_scripts/import_free_templates.py --sample-excel
```

这将创建一个名为 `sample_free_templates.xlsx` 的示例文件，包含以下工作表：
- 免费模板（所有数据）
- 黑白模板（blackWhite分类）
- 彩色模板（colorful分类）
- 现代模板（modern分类）

### 2. 查看工作表列表

```bash
python server_scripts/import_free_templates.py --list-sheets sample_free_templates.xlsx
```

输出示例：
```
Excel文件 'sample_free_templates.xlsx' 包含的工作表:
  1. 免费模板
  2. 黑白模板
  3. 彩色模板
  4. 现代模板
```

### 3. 导入整个Excel文件

```bash
python server_scripts/import_free_templates.py sample_free_templates.xlsx
```

### 4. 导入指定工作表

```bash
python server_scripts/import_free_templates.py sample_free_templates.xlsx --sheet "黑白模板"
```

## 数据格式要求

### 必需字段
- **id**: 模板ID，必须唯一，建议格式为 `分类/文件名.扩展名`

### 可选字段
- **batch_flag**: 批次标识，如果不提供，会从id中自动提取
- **thumb_path**: 缩略图路径，如果不提供，会自动生成
- **baidu_url**: 百度网盘链接
- **baidu_pass**: 百度网盘提取码
- **quark_url**: 夸克网盘链接
- **quark_pass**: 夸克网盘提取码
- **download_count**: 下载次数，默认为0
- **type**: 文件类型，默认为"word"

## 错误处理

脚本具有完善的错误处理机制：

1. **文件不存在**: 会提示文件路径错误
2. **工作表不存在**: 会列出可用的工作表名称
3. **数据格式错误**: 会跳过错误行并继续处理
4. **数据库连接错误**: 会回滚事务并提示错误信息

## 导入结果

导入完成后，脚本会显示详细的统计信息：

```
导入完成！
新增模板: 3 个
更新模板: 1 个
错误记录: 0 个
总计处理: 4 个模板
```

## 注意事项

1. **数据备份**: 导入前建议备份数据库
2. **重复数据**: 相同ID的记录会被更新而不是重复插入
3. **编码格式**: Excel文件请使用UTF-8编码保存
4. **工作表名称**: 工作表名称区分大小写
5. **依赖安装**: 确保已安装pandas和openpyxl库

## 依赖要求

```bash
pip install pandas>=2.0.0 openpyxl>=3.1.0
```

## 故障排除

### 常见问题

1. **ModuleNotFoundError**: 确保已安装所需依赖
2. **数据库连接失败**: 检查数据库配置和连接
3. **Excel文件损坏**: 尝试重新保存Excel文件
4. **权限问题**: 确保脚本有读取文件的权限

### 调试模式

脚本会显示详细的SQL执行日志，有助于调试数据库相关问题。

## 更新日志

- **v2.0**: 新增Excel文件支持和工作表选择功能
- **v1.0**: 基础CSV导入功能
