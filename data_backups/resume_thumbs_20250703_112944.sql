-- resume_thumbs 表数据备份
-- 导出时间: 2025-07-03T11:29:44.223907
-- 记录数量: 6

-- 表结构
DROP TABLE IF EXISTS resume_thumbs;
CREATE TABLE `resume_thumbs` (
  `id` varchar(100) NOT NULL,
  `batch_flag` varchar(50) NOT NULL,
  `thumb_path` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `sort_index` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_batch_flag` (`batch_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 数据插入
INSERT INTO resume_thumbs (id, batch_flag, thumb_path, created_at, updated_at, sort_index) VALUES
('template_001', 'template', 'resume_templates/template_A01.jpg', '2025-06-24T21:31:50', '2025-06-26T02:28:39', 1),
('template_002', 'template', 'resume_templates/template_A02.jpg', '2025-06-24T21:31:50', '2025-06-26T02:28:39', 2),
('template_003', 'template', 'resume_templates/template_A03.jpg', '2025-06-24T21:31:50', '2025-06-26T02:28:39', 3),
('template_004', 'template', 'resume_templates/活泼单页16.jpg', '2025-06-24T21:31:50', '2025-06-26T02:28:39', 4),
('template_005', 'template', 'resume_templates/稳重单页25.jpg', '2025-06-24T21:31:50', '2025-06-26T02:28:39', 5),
('template_006', 'template', 'resume_templates/文艺单页15.jpg', '2025-06-26T02:28:39', '2025-06-26T02:29:48', 6);
