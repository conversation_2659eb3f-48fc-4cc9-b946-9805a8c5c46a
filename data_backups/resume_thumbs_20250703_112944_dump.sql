-- MySQL dump 10.13  Distrib 8.0.42, for Linux (x86_64)
--
-- Host: localhost    Database: resume_service
-- ------------------------------------------------------
-- Server version	8.0.42-0ubuntu0.24.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `resume_thumbs`
--

DROP TABLE IF EXISTS `resume_thumbs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `resume_thumbs` (
  `id` varchar(100) NOT NULL,
  `batch_flag` varchar(50) NOT NULL,
  `thumb_path` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `sort_index` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_batch_flag` (`batch_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `resume_thumbs`
--

LOCK TABLES `resume_thumbs` WRITE;
/*!40000 ALTER TABLE `resume_thumbs` DISABLE KEYS */;
INSERT INTO `resume_thumbs` (`id`, `batch_flag`, `thumb_path`, `created_at`, `updated_at`, `sort_index`) VALUES ('template_001','template','resume_templates/template_A01.jpg','2025-06-24 13:31:50','2025-06-25 18:28:39',1);
INSERT INTO `resume_thumbs` (`id`, `batch_flag`, `thumb_path`, `created_at`, `updated_at`, `sort_index`) VALUES ('template_002','template','resume_templates/template_A02.jpg','2025-06-24 13:31:50','2025-06-25 18:28:39',2);
INSERT INTO `resume_thumbs` (`id`, `batch_flag`, `thumb_path`, `created_at`, `updated_at`, `sort_index`) VALUES ('template_003','template','resume_templates/template_A03.jpg','2025-06-24 13:31:50','2025-06-25 18:28:39',3);
INSERT INTO `resume_thumbs` (`id`, `batch_flag`, `thumb_path`, `created_at`, `updated_at`, `sort_index`) VALUES ('template_004','template','resume_templates/活泼单页16.jpg','2025-06-24 13:31:50','2025-06-25 18:28:39',4);
INSERT INTO `resume_thumbs` (`id`, `batch_flag`, `thumb_path`, `created_at`, `updated_at`, `sort_index`) VALUES ('template_005','template','resume_templates/稳重单页25.jpg','2025-06-24 13:31:50','2025-06-25 18:28:39',5);
INSERT INTO `resume_thumbs` (`id`, `batch_flag`, `thumb_path`, `created_at`, `updated_at`, `sort_index`) VALUES ('template_006','template','resume_templates/文艺单页15.jpg','2025-06-25 18:28:39','2025-06-25 18:29:48',6);
/*!40000 ALTER TABLE `resume_thumbs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping routines for database 'resume_service'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-03 11:29:44
