"""
数据库迁移脚本：为免费模板表添加排序优先级字段
"""
import mysql.connector
from mysql.connector import Error
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库连接配置
DB_CONFIG = {
    'host': 'localhost',
    'database': 'resume_service',
    'user': 'resume_user',
    'password': 'Resume123!'
}

def add_sort_order_field():
    """添加sort_order字段到free_templates表"""
    connection = None
    cursor = None

    try:
        # 连接数据库
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()

        logger.info("连接数据库成功")

        # 检查字段是否已存在
        check_sql = """
        SELECT COUNT(*)
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = 'resume_service'
        AND TABLE_NAME = 'free_templates'
        AND COLUMN_NAME = 'sort_order'
        """

        cursor.execute(check_sql)
        field_exists = cursor.fetchone()[0] > 0

        if field_exists:
            logger.info("sort_order字段已存在，跳过添加")
            return True

        # 添加sort_order字段
        alter_sql = """
        ALTER TABLE free_templates
        ADD COLUMN sort_order INT NOT NULL DEFAULT 0 COMMENT '排序优先级，数值越小越靠前'
        """

        cursor.execute(alter_sql)
        connection.commit()

        logger.info("成功添加sort_order字段到free_templates表")

        # 为现有数据设置默认排序值（按创建时间倒序）
        # 使用简单的方法，先获取所有记录，然后逐个更新
        cursor.execute("SELECT id FROM free_templates ORDER BY created_at DESC")
        template_ids = cursor.fetchall()

        # 逐个更新排序值
        for index, (template_id,) in enumerate(template_ids, start=1):
            update_sql = "UPDATE free_templates SET sort_order = %s WHERE id = %s"
            cursor.execute(update_sql, (index, template_id))

        connection.commit()

        logger.info(f"成功为现有数据设置默认排序值，共更新 {len(template_ids)} 条记录")

        # 验证字段添加成功
        cursor.execute(check_sql)
        field_exists = cursor.fetchone()[0] > 0

        if field_exists:
            logger.info("字段添加验证成功")
            return True
        else:
            logger.error("字段添加验证失败")
            return False

    except Error as e:
        logger.error(f"数据库操作错误: {e}")
        if connection:
            connection.rollback()
        return False

    finally:
        if cursor:
            cursor.close()
        if connection and connection.is_connected():
            connection.close()
            logger.info("数据库连接已关闭")

def show_table_structure():
    """显示free_templates表结构"""
    connection = None
    cursor = None

    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()

        cursor.execute("DESCRIBE free_templates")
        columns = cursor.fetchall()

        logger.info("free_templates表结构:")
        for column in columns:
            logger.info(f"  {column[0]} - {column[1]} - {column[2]} - {column[3]} - {column[4]} - {column[5]}")

    except Error as e:
        logger.error(f"查询表结构错误: {e}")

    finally:
        if cursor:
            cursor.close()
        if connection and connection.is_connected():
            connection.close()

def show_sample_data():
    """显示示例数据"""
    connection = None
    cursor = None

    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()

        cursor.execute("SELECT id, batch_flag, sort_order, created_at FROM free_templates ORDER BY sort_order LIMIT 10")
        rows = cursor.fetchall()

        logger.info("示例数据（按sort_order排序）:")
        for row in rows:
            logger.info(f"  ID: {row[0]}, 批次: {row[1]}, 排序: {row[2]}, 创建时间: {row[3]}")

    except Error as e:
        logger.error(f"查询示例数据错误: {e}")

    finally:
        if cursor:
            cursor.close()
        if connection and connection.is_connected():
            connection.close()

def main():
    """主函数"""
    logger.info("开始数据库迁移：为免费模板表添加排序优先级字段")

    # 显示迁移前的表结构
    logger.info("=== 迁移前表结构 ===")
    show_table_structure()

    # 执行迁移
    success = add_sort_order_field()

    if success:
        logger.info("=== 迁移后表结构 ===")
        show_table_structure()

        logger.info("=== 示例数据 ===")
        show_sample_data()

        logger.info("数据库迁移完成！")
        logger.info("现在可以使用sort_order字段进行自定义排序了")
    else:
        logger.error("数据库迁移失败！")

if __name__ == "__main__":
    main()
