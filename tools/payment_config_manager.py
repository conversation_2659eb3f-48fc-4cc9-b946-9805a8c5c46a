#!/usr/bin/env python3
"""
支付配置管理工具
用于管理不同环境的微信支付配置
"""
import os
import json
import argparse
import logging
from pathlib import Path
from typing import Dict, Any, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class PaymentConfigManager:
    """支付配置管理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.env_file = self.project_root / ".env"
        self.config_dir = self.project_root / "config" / "environments"
        self.config_dir.mkdir(parents=True, exist_ok=True)
    
    def create_environment_config(self, env_name: str, config: Dict[str, Any]):
        """创建环境配置文件"""
        try:
            config_file = self.config_dir / f"{env_name}.json"
            
            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            logger.info(f"环境配置已保存: {config_file}")
            return True
            
        except Exception as e:
            logger.error(f"创建环境配置失败: {e}")
            return False
    
    def load_environment_config(self, env_name: str) -> Optional[Dict[str, Any]]:
        """加载环境配置"""
        try:
            config_file = self.config_dir / f"{env_name}.json"
            
            if not config_file.exists():
                logger.error(f"环境配置文件不存在: {config_file}")
                return None
            
            with open(config_file, "r", encoding="utf-8") as f:
                config = json.load(f)
            
            logger.info(f"环境配置已加载: {config_file}")
            return config
            
        except Exception as e:
            logger.error(f"加载环境配置失败: {e}")
            return None
    
    def switch_environment(self, env_name: str) -> bool:
        """切换到指定环境"""
        try:
            # 加载环境配置
            config = self.load_environment_config(env_name)
            if not config:
                return False
            
            # 读取现有环境变量
            env_vars = {}
            if self.env_file.exists():
                with open(self.env_file, "r") as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith("#") and "=" in line:
                            key, value = line.split("=", 1)
                            env_vars[key.strip()] = value.strip()
            
            # 更新支付相关配置
            payment_keys = [
                "WECHAT_PAY_APPID",
                "WECHAT_PAY_MCHID", 
                "WECHAT_PAY_API_KEY",
                "WECHAT_PAY_API_V3_KEY",
                "WECHAT_PAY_CERT_PATH",
                "WECHAT_PAY_KEY_PATH",
                "WECHAT_PAY_CERT_SERIAL_NO",
                "WECHAT_PAY_NOTIFY_URL",
                "WECHAT_PAY_SANDBOX",
                "ORDER_EXPIRE_MINUTES",
                "ORDER_PREFIX"
            ]
            
            for key in payment_keys:
                if key in config:
                    env_vars[key] = str(config[key])
            
            # 添加环境标识
            env_vars["PAYMENT_ENV"] = env_name
            
            # 写入环境变量文件
            with open(self.env_file, "w") as f:
                for key, value in env_vars.items():
                    f.write(f"{key}={value}\n")
            
            logger.info(f"已切换到 {env_name} 环境")
            return True
            
        except Exception as e:
            logger.error(f"切换环境失败: {e}")
            return False
    
    def list_environments(self) -> list:
        """列出所有可用环境"""
        try:
            environments = []
            for config_file in self.config_dir.glob("*.json"):
                env_name = config_file.stem
                environments.append(env_name)
            
            return sorted(environments)
            
        except Exception as e:
            logger.error(f"列出环境失败: {e}")
            return []
    
    def get_current_environment(self) -> Optional[str]:
        """获取当前环境"""
        try:
            if not self.env_file.exists():
                return None
            
            with open(self.env_file, "r") as f:
                for line in f:
                    line = line.strip()
                    if line.startswith("PAYMENT_ENV="):
                        return line.split("=", 1)[1]
            
            return None
            
        except Exception as e:
            logger.error(f"获取当前环境失败: {e}")
            return None
    
    def validate_config(self, config: Dict[str, Any]) -> tuple:
        """验证配置"""
        errors = []
        warnings = []
        
        # 必需字段检查
        required_fields = [
            "WECHAT_PAY_APPID",
            "WECHAT_PAY_MCHID",
            "WECHAT_PAY_API_V3_KEY",
            "WECHAT_PAY_CERT_SERIAL_NO",
            "WECHAT_PAY_NOTIFY_URL"
        ]
        
        for field in required_fields:
            if not config.get(field):
                errors.append(f"缺少必需字段: {field}")
        
        # 证书文件检查
        cert_path = config.get("WECHAT_PAY_CERT_PATH")
        key_path = config.get("WECHAT_PAY_KEY_PATH")
        
        if cert_path and not Path(cert_path).exists():
            warnings.append(f"证书文件不存在: {cert_path}")
        
        if key_path and not Path(key_path).exists():
            warnings.append(f"私钥文件不存在: {key_path}")
        
        # URL格式检查
        notify_url = config.get("WECHAT_PAY_NOTIFY_URL")
        if notify_url and not notify_url.startswith(("http://", "https://")):
            errors.append("WECHAT_PAY_NOTIFY_URL 必须是有效的HTTP/HTTPS URL")
        
        return errors, warnings
    
    def create_default_configs(self):
        """创建默认配置文件"""
        # 开发环境配置
        dev_config = {
            "WECHAT_PAY_APPID": "wx_dev_appid_here",
            "WECHAT_PAY_MCHID": "dev_mchid_here",
            "WECHAT_PAY_API_V3_KEY": "dev_api_v3_key_here",
            "WECHAT_PAY_CERT_PATH": "certs/dev_apiclient_cert.pem",
            "WECHAT_PAY_KEY_PATH": "certs/dev_apiclient_key.pem",
            "WECHAT_PAY_CERT_SERIAL_NO": "dev_cert_serial_no_here",
            "WECHAT_PAY_NOTIFY_URL": "https://dev.your-domain.com/payment/callback",
            "WECHAT_PAY_SANDBOX": "true",
            "ORDER_EXPIRE_MINUTES": "30",
            "ORDER_PREFIX": "DEV"
        }
        
        # 测试环境配置
        test_config = {
            "WECHAT_PAY_APPID": "wx_test_appid_here",
            "WECHAT_PAY_MCHID": "test_mchid_here",
            "WECHAT_PAY_API_V3_KEY": "test_api_v3_key_here",
            "WECHAT_PAY_CERT_PATH": "certs/test_apiclient_cert.pem",
            "WECHAT_PAY_KEY_PATH": "certs/test_apiclient_key.pem",
            "WECHAT_PAY_CERT_SERIAL_NO": "test_cert_serial_no_here",
            "WECHAT_PAY_NOTIFY_URL": "https://test.your-domain.com/payment/callback",
            "WECHAT_PAY_SANDBOX": "true",
            "ORDER_EXPIRE_MINUTES": "30",
            "ORDER_PREFIX": "TEST"
        }
        
        # 生产环境配置
        prod_config = {
            "WECHAT_PAY_APPID": "wx_prod_appid_here",
            "WECHAT_PAY_MCHID": "prod_mchid_here",
            "WECHAT_PAY_API_V3_KEY": "prod_api_v3_key_here",
            "WECHAT_PAY_CERT_PATH": "certs/prod_apiclient_cert.pem",
            "WECHAT_PAY_KEY_PATH": "certs/prod_apiclient_key.pem",
            "WECHAT_PAY_CERT_SERIAL_NO": "prod_cert_serial_no_here",
            "WECHAT_PAY_NOTIFY_URL": "https://your-domain.com/payment/callback",
            "WECHAT_PAY_SANDBOX": "false",
            "ORDER_EXPIRE_MINUTES": "30",
            "ORDER_PREFIX": "RS"
        }
        
        # 创建配置文件
        self.create_environment_config("development", dev_config)
        self.create_environment_config("testing", test_config)
        self.create_environment_config("production", prod_config)
        
        logger.info("默认配置文件已创建")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="支付配置管理工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 列出环境
    subparsers.add_parser("list", help="列出所有可用环境")
    
    # 切换环境
    switch_parser = subparsers.add_parser("switch", help="切换到指定环境")
    switch_parser.add_argument("environment", help="环境名称")
    
    # 获取当前环境
    subparsers.add_parser("current", help="获取当前环境")
    
    # 验证配置
    validate_parser = subparsers.add_parser("validate", help="验证指定环境的配置")
    validate_parser.add_argument("environment", help="环境名称")
    
    # 创建默认配置
    subparsers.add_parser("init", help="创建默认配置文件")
    
    args = parser.parse_args()
    
    manager = PaymentConfigManager()
    
    if args.command == "list":
        environments = manager.list_environments()
        current = manager.get_current_environment()
        
        print("可用环境:")
        for env in environments:
            marker = " (当前)" if env == current else ""
            print(f"  - {env}{marker}")
    
    elif args.command == "switch":
        if manager.switch_environment(args.environment):
            print(f"已切换到 {args.environment} 环境")
        else:
            print(f"切换到 {args.environment} 环境失败")
    
    elif args.command == "current":
        current = manager.get_current_environment()
        if current:
            print(f"当前环境: {current}")
        else:
            print("未设置环境")
    
    elif args.command == "validate":
        config = manager.load_environment_config(args.environment)
        if config:
            errors, warnings = manager.validate_config(config)
            
            if errors:
                print("配置错误:")
                for error in errors:
                    print(f"  - {error}")
            
            if warnings:
                print("配置警告:")
                for warning in warnings:
                    print(f"  - {warning}")
            
            if not errors and not warnings:
                print("配置验证通过")
        else:
            print(f"环境 {args.environment} 不存在")
    
    elif args.command == "init":
        manager.create_default_configs()
        print("默认配置文件已创建")
    
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
