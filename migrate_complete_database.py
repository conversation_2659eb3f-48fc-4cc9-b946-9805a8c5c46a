#!/usr/bin/env python3
"""
完整数据库迁移脚本
创建所有数据库表结构，支持从零开始创建整个数据库
"""
import mysql.connector
from mysql.connector import Error
import logging
import sys
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库连接配置
DB_CONFIG = {
    'host': 'localhost',
    'database': 'resume_service',
    'user': 'resume_user',
    'password': 'Resume123!'
}

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        return connection
    except Error as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def check_table_exists(connection, table_name):
    """检查表是否存在"""
    try:
        cursor = connection.cursor()
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = '{DB_CONFIG['database']}' 
            AND table_name = '{table_name}'
        """)
        result = cursor.fetchone()
        cursor.close()
        return result[0] > 0
    except Error as e:
        logger.error(f"检查表存在性失败: {e}")
        return False

def create_users_table(connection):
    """创建用户表"""
    try:
        cursor = connection.cursor()
        
        create_table_sql = """
        CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            openid VARCHAR(100) UNIQUE NOT NULL COMMENT '微信openid',
            unionid VARCHAR(100) NULL COMMENT '微信unionid',
            nickname VARCHAR(100) NULL COMMENT '用户昵称',
            avatar_url TEXT NULL COMMENT '头像URL',
            gender INT NULL COMMENT '性别：0未知，1男，2女',
            country VARCHAR(50) NULL COMMENT '国家',
            province VARCHAR(50) NULL COMMENT '省份',
            city VARCHAR(50) NULL COMMENT '城市',
            is_member BOOLEAN DEFAULT FALSE NOT NULL COMMENT '是否会员',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            
            INDEX idx_id (id),
            INDEX idx_openid (openid),
            INDEX idx_is_member (is_member),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
        """
        
        cursor.execute(create_table_sql)
        connection.commit()
        logger.info("用户表创建成功")
        cursor.close()
        return True
        
    except Error as e:
        logger.error(f"创建用户表失败: {e}")
        connection.rollback()
        return False

def create_user_actions_table(connection):
    """创建用户行为记录表"""
    try:
        cursor = connection.cursor()
        
        create_table_sql = """
        CREATE TABLE user_actions (
            id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            user_id INT NOT NULL COMMENT '用户ID',
            action_type VARCHAR(50) NOT NULL COMMENT '行为类型',
            action_content JSON NULL COMMENT '行为详细内容',
            template_id VARCHAR(50) NULL COMMENT '使用的模板ID',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            
            INDEX idx_id (id),
            INDEX idx_user_id (user_id),
            INDEX idx_action_type (action_type),
            INDEX idx_template_id (template_id),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户行为记录表';
        """
        
        cursor.execute(create_table_sql)
        connection.commit()
        logger.info("用户行为记录表创建成功")
        cursor.close()
        return True
        
    except Error as e:
        logger.error(f"创建用户行为记录表失败: {e}")
        connection.rollback()
        return False

def create_feedback_table(connection):
    """创建反馈表"""
    try:
        cursor = connection.cursor()
        
        create_table_sql = """
        CREATE TABLE feedback (
            id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            user_id INT NOT NULL COMMENT '用户ID',
            content TEXT NOT NULL COMMENT '反馈内容',
            contact_info VARCHAR(200) NULL COMMENT '联系方式',
            status ENUM('pending', 'replied', 'closed') DEFAULT 'pending' COMMENT '状态',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            
            INDEX idx_id (id),
            INDEX idx_user_id (user_id),
            INDEX idx_status (status),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='反馈表';
        """
        
        cursor.execute(create_table_sql)
        connection.commit()
        logger.info("反馈表创建成功")
        cursor.close()
        return True
        
    except Error as e:
        logger.error(f"创建反馈表失败: {e}")
        connection.rollback()
        return False

def create_feedback_replies_table(connection):
    """创建反馈回复表"""
    try:
        cursor = connection.cursor()
        
        create_table_sql = """
        CREATE TABLE feedback_replies (
            id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            feedback_id INT NOT NULL COMMENT '反馈ID',
            admin_name VARCHAR(100) NOT NULL COMMENT '管理员名称',
            reply_content TEXT NOT NULL COMMENT '回复内容',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            
            INDEX idx_id (id),
            INDEX idx_feedback_id (feedback_id),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='反馈回复表';
        """
        
        cursor.execute(create_table_sql)
        connection.commit()
        logger.info("反馈回复表创建成功")
        cursor.close()
        return True
        
    except Error as e:
        logger.error(f"创建反馈回复表失败: {e}")
        connection.rollback()
        return False

def create_free_templates_table(connection):
    """创建免费简历模板表"""
    try:
        cursor = connection.cursor()
        
        create_table_sql = """
        CREATE TABLE free_templates (
            id VARCHAR(100) PRIMARY KEY COMMENT '模板ID，如：blackWhite/10.jpg',
            batch_flag VARCHAR(50) NOT NULL COMMENT '批次标识，如：blackWhite',
            thumb_path VARCHAR(255) NOT NULL COMMENT '缩略图路径',
            baidu_url VARCHAR(500) NULL COMMENT '百度网盘链接',
            baidu_pass VARCHAR(20) NULL COMMENT '百度网盘提取码',
            quark_url VARCHAR(500) NULL COMMENT '夸克网盘链接',
            quark_pass VARCHAR(20) NULL COMMENT '夸克网盘提取码',
            download_count INT DEFAULT 0 NOT NULL COMMENT '下载次数',
            type VARCHAR(20) DEFAULT 'word' NOT NULL COMMENT '文件类型',
            sort_order INT DEFAULT 0 NOT NULL COMMENT '排序优先级，数值越小越靠前',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            
            INDEX idx_id (id),
            INDEX idx_batch_flag (batch_flag),
            INDEX idx_download_count (download_count),
            INDEX idx_type (type),
            INDEX idx_sort_order (sort_order),
            INDEX idx_batch_type_sort (batch_flag, type, sort_order),
            INDEX idx_type_download (type, download_count),
            INDEX idx_sort_created (sort_order, created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='免费简历模板表';
        """
        
        cursor.execute(create_table_sql)
        connection.commit()
        logger.info("免费简历模板表创建成功")
        cursor.close()
        return True
        
    except Error as e:
        logger.error(f"创建免费简历模板表失败: {e}")
        connection.rollback()
        return False

def create_resume_thumbs_table(connection):
    """创建简历缩略图表"""
    try:
        cursor = connection.cursor()
        
        create_table_sql = """
        CREATE TABLE resume_thumbs (
            id VARCHAR(100) PRIMARY KEY COMMENT '缩略图ID',
            batch_flag VARCHAR(50) NOT NULL COMMENT '批次标识',
            thumb_path VARCHAR(255) NOT NULL COMMENT '缩略图路径',
            sort_index INT DEFAULT 0 NOT NULL COMMENT '排序索引',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            
            INDEX idx_id (id),
            INDEX idx_batch_flag (batch_flag),
            INDEX idx_sort_index (sort_index),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历缩略图表';
        """
        
        cursor.execute(create_table_sql)
        connection.commit()
        logger.info("简历缩略图表创建成功")
        cursor.close()
        return True

    except Error as e:
        logger.error(f"创建简历缩略图表失败: {e}")
        connection.rollback()
        return False

def create_error_reports_table(connection):
    """创建错误上报表"""
    try:
        cursor = connection.cursor()

        create_table_sql = """
        CREATE TABLE error_reports (
            id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            user_id INT NULL COMMENT '用户ID，可为空（未登录用户）',
            openid VARCHAR(100) NULL COMMENT '微信openid，可为空',
            error_type VARCHAR(100) NOT NULL COMMENT '错误类型',
            error_message TEXT NOT NULL COMMENT '错误信息',
            error_context JSON NULL COMMENT '错误上下文信息（包含所有动态内容）',

            -- 时间字段
            timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '错误发生时间',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '上报时间',
            reported_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '实际上报时间',

            -- 请求相关信息
            request_url VARCHAR(500) NULL COMMENT '请求URL',
            request_method VARCHAR(10) NULL COMMENT '请求方法（GET/POST等）',
            request_params JSON NULL COMMENT '请求参数',
            response_status INT NULL COMMENT '响应状态码',
            response_time INT NULL COMMENT '响应时间（毫秒）',

            -- 用户会话信息
            session_id VARCHAR(100) NULL COMMENT '会话ID',
            user_ip VARCHAR(45) NULL COMMENT '用户IP地址',
            user_location JSON NULL COMMENT '用户地理位置信息',

            -- 应用状态信息
            app_state JSON NULL COMMENT '应用状态信息',
            memory_usage INT NULL COMMENT '内存使用情况（MB）',
            battery_level INT NULL COMMENT '电池电量（百分比）',

            -- 保留兼容字段
            error_stack TEXT NULL COMMENT '错误堆栈（兼容字段）',
            page_path VARCHAR(255) NULL COMMENT '发生错误的页面路径（兼容字段）',
            user_agent TEXT NULL COMMENT '用户代理信息（兼容字段）',
            device_info JSON NULL COMMENT '设备信息（兼容字段）',
            app_version VARCHAR(50) NULL COMMENT '应用版本（兼容字段）',
            system_info JSON NULL COMMENT '系统信息（兼容字段）',
            network_type VARCHAR(50) NULL COMMENT '网络类型（兼容字段）',

            -- 索引
            INDEX idx_id (id),
            INDEX idx_user_id (user_id),
            INDEX idx_openid (openid),
            INDEX idx_error_type (error_type),
            INDEX idx_error_type_created (error_type, created_at),
            INDEX idx_user_created (user_id, created_at),
            INDEX idx_openid_created (openid, created_at),
            INDEX idx_timestamp_created (timestamp, created_at),
            INDEX idx_session_created (session_id, created_at),
            INDEX idx_request_url (request_url),
            INDEX idx_response_status (response_status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='错误上报表 - 增强版本';
        """

        cursor.execute(create_table_sql)
        connection.commit()
        logger.info("错误上报表创建成功")
        cursor.close()
        return True

    except Error as e:
        logger.error(f"创建错误上报表失败: {e}")
        connection.rollback()
        return False

def add_foreign_key_constraints(connection):
    """添加外键约束（可选）"""
    try:
        cursor = connection.cursor()

        # 外键约束列表
        foreign_keys = [
            # 用户行为表的外键
            ("user_actions", "fk_user_actions_user_id", "user_id", "users", "id"),
            # 反馈表的外键
            ("feedback", "fk_feedback_user_id", "user_id", "users", "id"),
            # 反馈回复表的外键
            ("feedback_replies", "fk_feedback_replies_feedback_id", "feedback_id", "feedback", "id"),
            # 错误上报表的外键（可为空）
            ("error_reports", "fk_error_reports_user_id", "user_id", "users", "id"),
        ]

        added_constraints = []
        for table_name, constraint_name, column_name, ref_table, ref_column in foreign_keys:
            try:
                if table_name == "error_reports":
                    # 错误上报表的外键允许NULL值
                    alter_sql = f"""
                    ALTER TABLE {table_name}
                    ADD CONSTRAINT {constraint_name}
                    FOREIGN KEY ({column_name}) REFERENCES {ref_table}({ref_column})
                    ON DELETE SET NULL ON UPDATE CASCADE
                    """
                else:
                    # 其他表的外键不允许NULL值
                    alter_sql = f"""
                    ALTER TABLE {table_name}
                    ADD CONSTRAINT {constraint_name}
                    FOREIGN KEY ({column_name}) REFERENCES {ref_table}({ref_column})
                    ON DELETE CASCADE ON UPDATE CASCADE
                    """

                cursor.execute(alter_sql)
                connection.commit()
                added_constraints.append(constraint_name)
                logger.info(f"外键约束 {constraint_name} 添加成功")

            except Error as e:
                if "Duplicate key name" in str(e) or "already exists" in str(e):
                    logger.info(f"外键约束 {constraint_name} 已存在，跳过")
                else:
                    logger.warning(f"添加外键约束 {constraint_name} 失败: {e}")
                    connection.rollback()

        cursor.close()
        return added_constraints

    except Error as e:
        logger.error(f"添加外键约束失败: {e}")
        connection.rollback()
        return []

def main():
    """主函数"""
    logger.info("开始完整数据库迁移...")

    # 获取数据库连接
    connection = get_db_connection()
    if not connection:
        logger.error("无法连接到数据库，迁移失败")
        sys.exit(1)

    try:
        # 定义所有表的创建函数
        table_creators = [
            ("users", create_users_table),
            ("user_actions", create_user_actions_table),
            ("feedback", create_feedback_table),
            ("feedback_replies", create_feedback_replies_table),
            ("free_templates", create_free_templates_table),
            ("resume_thumbs", create_resume_thumbs_table),
            ("error_reports", create_error_reports_table),
        ]

        created_tables = []
        skipped_tables = []

        # 创建所有表
        for table_name, creator_func in table_creators:
            if check_table_exists(connection, table_name):
                logger.info(f"表 {table_name} 已存在，跳过创建")
                skipped_tables.append(table_name)
            else:
                if creator_func(connection):
                    created_tables.append(table_name)
                else:
                    logger.error(f"创建表 {table_name} 失败")
                    sys.exit(1)

        # 添加外键约束（可选）
        try:
            added_constraints = add_foreign_key_constraints(connection)
            if added_constraints:
                logger.info(f"成功添加 {len(added_constraints)} 个外键约束")
        except Exception as e:
            logger.warning(f"添加外键约束失败，但不影响主要功能: {e}")

        # 输出迁移结果
        logger.info("=" * 60)
        logger.info("数据库迁移完成！")
        logger.info(f"新创建的表: {created_tables}")
        logger.info(f"已存在的表: {skipped_tables}")
        logger.info("=" * 60)

        # 显示所有表的状态
        cursor = connection.cursor()
        cursor.execute(f"SHOW TABLES FROM {DB_CONFIG['database']}")
        tables = cursor.fetchall()

        logger.info("当前数据库中的所有表:")
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            logger.info(f"  - {table_name}: {count} 条记录")

        cursor.close()

    except Exception as e:
        logger.exception("迁移过程中发生异常")
        sys.exit(1)

    finally:
        if connection.is_connected():
            connection.close()
            logger.info("数据库连接已关闭")

if __name__ == "__main__":
    main()
