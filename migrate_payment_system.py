#!/usr/bin/env python3
"""
支付系统数据库迁移脚本
创建支付相关的数据库表结构
"""
import mysql.connector
from mysql.connector import Error
import logging
import sys
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库连接配置
DB_CONFIG = {
    'host': 'localhost',
    'database': 'resume_service',
    'user': 'resume_user',
    'password': 'Resume123!'
}

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        if connection.is_connected():
            logger.info("数据库连接成功")
            return connection
    except Error as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def check_table_exists(cursor, table_name):
    """检查表是否存在"""
    cursor.execute(f"""
        SELECT COUNT(*) 
        FROM information_schema.tables 
        WHERE table_schema = '{DB_CONFIG['database']}' 
        AND table_name = '{table_name}'
    """)
    return cursor.fetchone()[0] > 0

def create_membership_plans_table(cursor):
    """创建会员套餐表"""
    table_name = "membership_plans"
    
    if check_table_exists(cursor, table_name):
        logger.info(f"表 {table_name} 已存在，跳过创建")
        return True
    
    try:
        create_table_sql = """
        CREATE TABLE membership_plans (
            id INT PRIMARY KEY AUTO_INCREMENT COMMENT '套餐ID',
            name VARCHAR(100) NOT NULL COMMENT '套餐名称',
            description TEXT COMMENT '套餐描述',
            price DECIMAL(10,2) NOT NULL COMMENT '价格(分)',
            original_price DECIMAL(10,2) COMMENT '原价(分)',
            duration_days INT NOT NULL COMMENT '有效期(天)',
            features JSON COMMENT '套餐特权',
            is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
            is_recommended BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
            sort_order INT DEFAULT 0 COMMENT '排序',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            
            INDEX idx_active_sort (is_active, sort_order),
            INDEX idx_price (price)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员套餐表';
        """
        
        cursor.execute(create_table_sql)
        logger.info(f"表 {table_name} 创建成功")
        return True
        
    except Error as e:
        logger.error(f"创建表 {table_name} 失败: {e}")
        return False

def create_orders_table(cursor):
    """创建订单表"""
    table_name = "orders"
    
    if check_table_exists(cursor, table_name):
        logger.info(f"表 {table_name} 已存在，跳过创建")
        return True
    
    try:
        create_table_sql = """
        CREATE TABLE orders (
            id VARCHAR(32) PRIMARY KEY COMMENT '订单号',
            user_id INT NOT NULL COMMENT '用户ID',
            plan_id INT NOT NULL COMMENT '套餐ID',
            amount DECIMAL(10,2) NOT NULL COMMENT '订单金额(分)',
            original_amount DECIMAL(10,2) COMMENT '原始金额(分)',
            discount_amount DECIMAL(10,2) DEFAULT 0 COMMENT '优惠金额(分)',
            status ENUM('pending', 'paid', 'cancelled', 'expired', 'refunded') DEFAULT 'pending' COMMENT '订单状态',
            wx_prepay_id VARCHAR(64) COMMENT '微信预支付ID',
            wx_transaction_id VARCHAR(32) COMMENT '微信交易号',
            client_ip VARCHAR(45) COMMENT '客户端IP',
            user_agent TEXT COMMENT '用户代理',
            paid_at TIMESTAMP NULL COMMENT '支付时间',
            expired_at TIMESTAMP NOT NULL COMMENT '订单过期时间',
            cancelled_at TIMESTAMP NULL COMMENT '取消时间',
            cancel_reason VARCHAR(255) COMMENT '取消原因',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (plan_id) REFERENCES membership_plans(id),
            INDEX idx_user_status (user_id, status),
            INDEX idx_status_created (status, created_at),
            INDEX idx_wx_transaction (wx_transaction_id),
            INDEX idx_expired (expired_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';
        """
        
        cursor.execute(create_table_sql)
        logger.info(f"表 {table_name} 创建成功")
        return True
        
    except Error as e:
        logger.error(f"创建表 {table_name} 失败: {e}")
        return False

def create_payment_records_table(cursor):
    """创建支付记录表"""
    table_name = "payment_records"
    
    if check_table_exists(cursor, table_name):
        logger.info(f"表 {table_name} 已存在，跳过创建")
        return True
    
    try:
        create_table_sql = """
        CREATE TABLE payment_records (
            id INT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
            order_id VARCHAR(32) NOT NULL COMMENT '订单号',
            wx_transaction_id VARCHAR(32) COMMENT '微信交易号',
            payment_method VARCHAR(20) DEFAULT 'wechat' COMMENT '支付方式',
            amount DECIMAL(10,2) NOT NULL COMMENT '支付金额(分)',
            status ENUM('success', 'failed', 'refunded', 'partial_refunded') DEFAULT 'success' COMMENT '支付状态',
            callback_data JSON COMMENT '回调原始数据',
            refund_amount DECIMAL(10,2) DEFAULT 0 COMMENT '退款金额(分)',
            refund_reason VARCHAR(255) COMMENT '退款原因',
            refunded_at TIMESTAMP NULL COMMENT '退款时间',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            
            FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
            UNIQUE KEY uk_wx_transaction (wx_transaction_id),
            INDEX idx_order_status (order_id, status),
            INDEX idx_payment_method (payment_method),
            INDEX idx_created (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付记录表';
        """
        
        cursor.execute(create_table_sql)
        logger.info(f"表 {table_name} 创建成功")
        return True
        
    except Error as e:
        logger.error(f"创建表 {table_name} 失败: {e}")
        return False

def create_user_memberships_table(cursor):
    """创建用户会员记录表"""
    table_name = "user_memberships"
    
    if check_table_exists(cursor, table_name):
        logger.info(f"表 {table_name} 已存在，跳过创建")
        return True
    
    try:
        create_table_sql = """
        CREATE TABLE user_memberships (
            id INT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
            user_id INT NOT NULL COMMENT '用户ID',
            order_id VARCHAR(32) NOT NULL COMMENT '订单号',
            plan_id INT NOT NULL COMMENT '套餐ID',
            start_date TIMESTAMP NOT NULL COMMENT '开始时间',
            end_date TIMESTAMP NOT NULL COMMENT '结束时间',
            is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
            auto_renew BOOLEAN DEFAULT FALSE COMMENT '是否自动续费',
            activated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '激活时间',
            deactivated_at TIMESTAMP NULL COMMENT '失效时间',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (order_id) REFERENCES orders(id),
            FOREIGN KEY (plan_id) REFERENCES membership_plans(id),
            INDEX idx_user_active (user_id, is_active),
            INDEX idx_end_date (end_date),
            INDEX idx_user_end_date (user_id, end_date),
            INDEX idx_active_end_date (is_active, end_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会员记录表';
        """
        
        cursor.execute(create_table_sql)
        logger.info(f"表 {table_name} 创建成功")
        return True
        
    except Error as e:
        logger.error(f"创建表 {table_name} 失败: {e}")
        return False

def create_payment_logs_table(cursor):
    """创建支付日志表"""
    table_name = "payment_logs"
    
    if check_table_exists(cursor, table_name):
        logger.info(f"表 {table_name} 已存在，跳过创建")
        return True
    
    try:
        create_table_sql = """
        CREATE TABLE payment_logs (
            id INT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
            order_id VARCHAR(32) COMMENT '订单号',
            user_id INT COMMENT '用户ID',
            action VARCHAR(50) NOT NULL COMMENT '操作类型',
            status VARCHAR(20) COMMENT '操作状态',
            request_data JSON COMMENT '请求数据',
            response_data JSON COMMENT '响应数据',
            error_message TEXT COMMENT '错误信息',
            ip_address VARCHAR(45) COMMENT 'IP地址',
            user_agent TEXT COMMENT '用户代理',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            
            INDEX idx_order_action (order_id, action),
            INDEX idx_user_action (user_id, action),
            INDEX idx_action_created (action, created_at),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付日志表';
        """
        
        cursor.execute(create_table_sql)
        logger.info(f"表 {table_name} 创建成功")
        return True
        
    except Error as e:
        logger.error(f"创建表 {table_name} 失败: {e}")
        return False

def insert_default_membership_plans(cursor):
    """插入默认会员套餐数据"""
    try:
        # 检查是否已有数据
        cursor.execute("SELECT COUNT(*) FROM membership_plans")
        count = cursor.fetchone()[0]
        
        if count > 0:
            logger.info("会员套餐数据已存在，跳过插入")
            return True
        
        # 插入默认套餐数据
        plans_data = [
            {
                'name': '月度会员',
                'description': '享受一个月的会员特权',
                'price': 1980,  # 19.8元，以分为单位
                'original_price': 2980,
                'duration_days': 30,
                'features': '["无限制简历导出", "高级模板使用", "证件照生成", "优先客服支持"]',
                'is_recommended': False,
                'sort_order': 1
            },
            {
                'name': '季度会员',
                'description': '享受三个月的会员特权，更优惠',
                'price': 4980,  # 49.8元
                'original_price': 8940,  # 原价89.4元
                'duration_days': 90,
                'features': '["无限制简历导出", "高级模板使用", "证件照生成", "优先客服支持", "专属模板"]',
                'is_recommended': True,
                'sort_order': 2
            },
            {
                'name': '年度会员',
                'description': '享受一年的会员特权，最划算',
                'price': 9980,  # 99.8元
                'original_price': 23760,  # 原价237.6元
                'duration_days': 365,
                'features': '["无限制简历导出", "高级模板使用", "证件照生成", "优先客服支持", "专属模板", "一对一指导"]',
                'is_recommended': False,
                'sort_order': 3
            }
        ]
        
        insert_sql = """
        INSERT INTO membership_plans 
        (name, description, price, original_price, duration_days, features, is_recommended, sort_order)
        VALUES (%(name)s, %(description)s, %(price)s, %(original_price)s, %(duration_days)s, %(features)s, %(is_recommended)s, %(sort_order)s)
        """
        
        cursor.executemany(insert_sql, plans_data)
        logger.info(f"成功插入 {len(plans_data)} 个默认会员套餐")
        return True
        
    except Error as e:
        logger.error(f"插入默认会员套餐失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始支付系统数据库迁移...")
    
    connection = get_db_connection()
    if not connection:
        logger.error("无法连接到数据库，迁移终止")
        sys.exit(1)
    
    try:
        cursor = connection.cursor()
        
        # 创建表的顺序很重要，因为有外键约束
        tables_to_create = [
            ("会员套餐表", create_membership_plans_table),
            ("订单表", create_orders_table),
            ("支付记录表", create_payment_records_table),
            ("用户会员记录表", create_user_memberships_table),
            ("支付日志表", create_payment_logs_table)
        ]
        
        success_count = 0
        for table_desc, create_func in tables_to_create:
            logger.info(f"正在创建 {table_desc}...")
            if create_func(cursor):
                success_count += 1
            else:
                logger.error(f"创建 {table_desc} 失败")
        
        if success_count == len(tables_to_create):
            logger.info("所有表创建成功，开始插入默认数据...")
            
            # 插入默认数据
            if insert_default_membership_plans(cursor):
                connection.commit()
                logger.info("支付系统数据库迁移完成！")
            else:
                logger.error("插入默认数据失败")
                connection.rollback()
        else:
            logger.error(f"部分表创建失败，成功: {success_count}/{len(tables_to_create)}")
            connection.rollback()
    
    except Error as e:
        logger.error(f"迁移过程中发生错误: {e}")
        connection.rollback()
    
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()
            logger.info("数据库连接已关闭")

if __name__ == "__main__":
    main()
