#!/usr/bin/env python3
"""
错误上报表字段更新迁移脚本
用于在现有error_reports表中添加新字段
"""
import mysql.connector
from mysql.connector import Error
import logging
import sys

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库连接配置
DB_CONFIG = {
    'host': 'localhost',
    'database': 'resume_service',
    'user': 'resume_user',
    'password': 'Resume123!'
}

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        return connection
    except Error as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def check_table_exists(connection, table_name):
    """检查表是否存在"""
    try:
        cursor = connection.cursor()
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = '{DB_CONFIG['database']}' 
            AND table_name = '{table_name}'
        """)
        result = cursor.fetchone()
        cursor.close()
        return result[0] > 0
    except Error as e:
        logger.error(f"检查表存在性失败: {e}")
        return False

def check_column_exists(connection, table_name, column_name):
    """检查列是否存在"""
    try:
        cursor = connection.cursor()
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM information_schema.columns 
            WHERE table_schema = '{DB_CONFIG['database']}' 
            AND table_name = '{table_name}' 
            AND column_name = '{column_name}'
        """)
        result = cursor.fetchone()
        cursor.close()
        return result[0] > 0
    except Error as e:
        logger.error(f"检查列存在性失败: {e}")
        return False

def add_new_columns(connection):
    """添加新字段到error_reports表"""
    try:
        cursor = connection.cursor()
        
        # 需要添加的新字段列表
        new_columns = [
            # 时间字段更新
            ("reported_at", "ALTER TABLE error_reports ADD COLUMN reported_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '实际上报时间'"),
            
            # 请求相关信息
            ("request_url", "ALTER TABLE error_reports ADD COLUMN request_url VARCHAR(500) NULL COMMENT '请求URL'"),
            ("request_method", "ALTER TABLE error_reports ADD COLUMN request_method VARCHAR(10) NULL COMMENT '请求方法（GET/POST等）'"),
            ("request_params", "ALTER TABLE error_reports ADD COLUMN request_params JSON NULL COMMENT '请求参数'"),
            ("response_status", "ALTER TABLE error_reports ADD COLUMN response_status INT NULL COMMENT '响应状态码'"),
            ("response_time", "ALTER TABLE error_reports ADD COLUMN response_time INT NULL COMMENT '响应时间（毫秒）'"),
            
            # 用户会话信息
            ("session_id", "ALTER TABLE error_reports ADD COLUMN session_id VARCHAR(100) NULL COMMENT '会话ID'"),
            ("user_ip", "ALTER TABLE error_reports ADD COLUMN user_ip VARCHAR(45) NULL COMMENT '用户IP地址'"),
            ("user_location", "ALTER TABLE error_reports ADD COLUMN user_location JSON NULL COMMENT '用户地理位置信息'"),
            
            # 应用状态信息
            ("app_state", "ALTER TABLE error_reports ADD COLUMN app_state JSON NULL COMMENT '应用状态信息'"),
            ("memory_usage", "ALTER TABLE error_reports ADD COLUMN memory_usage INT NULL COMMENT '内存使用情况（MB）'"),
            ("battery_level", "ALTER TABLE error_reports ADD COLUMN battery_level INT NULL COMMENT '电池电量（百分比）'"),
            
            # 添加error_context字段（如果不存在）
            ("error_context", "ALTER TABLE error_reports ADD COLUMN error_context JSON NULL COMMENT '错误上下文信息（包含所有动态内容）'"),
        ]
        
        added_columns = []
        for column_name, alter_sql in new_columns:
            if not check_column_exists(connection, 'error_reports', column_name):
                try:
                    cursor.execute(alter_sql)
                    connection.commit()
                    added_columns.append(column_name)
                    logger.info(f"成功添加字段: {column_name}")
                except Error as e:
                    logger.error(f"添加字段 {column_name} 失败: {e}")
                    connection.rollback()
            else:
                logger.info(f"字段 {column_name} 已存在，跳过")
        
        cursor.close()
        return added_columns
        
    except Error as e:
        logger.error(f"添加新字段失败: {e}")
        connection.rollback()
        return []

def update_timestamp_column(connection):
    """更新timestamp字段为NOT NULL"""
    try:
        cursor = connection.cursor()
        
        # 首先更新所有NULL的timestamp为当前时间
        update_sql = """
        UPDATE error_reports 
        SET timestamp = created_at 
        WHERE timestamp IS NULL
        """
        cursor.execute(update_sql)
        
        # 然后修改字段为NOT NULL
        alter_sql = """
        ALTER TABLE error_reports 
        MODIFY COLUMN timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '错误发生时间'
        """
        cursor.execute(alter_sql)
        connection.commit()
        
        cursor.close()
        logger.info("成功更新timestamp字段为NOT NULL")
        return True
        
    except Error as e:
        logger.error(f"更新timestamp字段失败: {e}")
        connection.rollback()
        return False

def add_new_indexes(connection):
    """添加新的索引"""
    try:
        cursor = connection.cursor()
        
        # 需要添加的新索引
        new_indexes = [
            ("idx_session_created", "CREATE INDEX idx_session_created ON error_reports (session_id, created_at)"),
            ("idx_request_url", "CREATE INDEX idx_request_url ON error_reports (request_url)"),
            ("idx_response_status", "CREATE INDEX idx_response_status ON error_reports (response_status)"),
        ]
        
        added_indexes = []
        for index_name, create_sql in new_indexes:
            try:
                cursor.execute(create_sql)
                connection.commit()
                added_indexes.append(index_name)
                logger.info(f"成功添加索引: {index_name}")
            except Error as e:
                if "Duplicate key name" in str(e):
                    logger.info(f"索引 {index_name} 已存在，跳过")
                else:
                    logger.error(f"添加索引 {index_name} 失败: {e}")
                    connection.rollback()
        
        cursor.close()
        return added_indexes
        
    except Error as e:
        logger.error(f"添加新索引失败: {e}")
        connection.rollback()
        return []

def main():
    """主函数"""
    logger.info("开始错误上报表字段更新迁移...")
    
    # 获取数据库连接
    connection = get_db_connection()
    if not connection:
        logger.error("无法连接到数据库，迁移失败")
        sys.exit(1)
    
    try:
        # 检查表是否存在
        if not check_table_exists(connection, 'error_reports'):
            logger.error("error_reports表不存在，请先运行基础迁移脚本")
            sys.exit(1)
        
        # 添加新字段
        added_columns = add_new_columns(connection)
        if added_columns:
            logger.info(f"成功添加 {len(added_columns)} 个新字段: {', '.join(added_columns)}")
        
        # 更新timestamp字段
        if update_timestamp_column(connection=connection):
            logger.info("timestamp字段更新成功")
        
        # 添加新索引
        added_indexes = add_new_indexes(connection)
        if added_indexes:
            logger.info(f"成功添加 {len(added_indexes)} 个新索引: {', '.join(added_indexes)}")
        
        logger.info("错误上报表字段更新迁移完成")
        
    except Exception as e:
        logger.exception("迁移过程中发生异常")
        sys.exit(1)
    
    finally:
        if connection.is_connected():
            connection.close()
            logger.info("数据库连接已关闭")

if __name__ == "__main__":
    main()
