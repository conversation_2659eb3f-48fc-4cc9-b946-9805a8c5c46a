# 错误上报API文档

## 概述

错误上报系统为微信小程序提供错误收集和分析功能，支持简化版和兼容版两套接口。

## 简化版接口（推荐）

### 1. 单个错误上报

**接口地址：** `POST /error/report`

**请求格式：**
```json
{
  "error_type": "string",
  "error_message": "string", 
  "error_context": {
    "user_info": {
      "user_id": 123,
      "openid": "string",
      "has_token": true
    },
    "system_info": {
      "platform": "ios",
      "system": "iOS 16.1.1",
      "model": "iPhone 14"
    },
    "app_info": {
      "version": "1.0.0",
      "scene": "1001",
      "path": "pages/makeResume/makeResume"
    },
    "error_stack": "string",
    "timestamp": "2025-07-02T13:47:13.997200"
  }
}
```

**字段说明：**
- `error_type`: 错误类型（必填）
- `error_message`: 错误信息（必填）
- `error_context`: 错误上下文信息（可选），包含所有动态内容

**响应格式：**
```json
{
  "success": true,
  "message": "错误上报成功",
  "data": {
    "report_id": "error_14",
    "timestamp": "2025-07-02T13:47:14.006631"
  }
}
```

### 2. 批量错误上报

**接口地址：** `POST /error/report/batch`

**请求格式：**
```json
{
  "errors": [
    {
      "error_type": "api_error",
      "error_message": "网络请求失败",
      "error_context": {
        "user_info": {
          "openid": "test_openid_batch_1"
        },
        "api_info": {
          "url": "/api/user/login",
          "method": "POST",
          "status_code": 500
        }
      }
    },
    {
      "error_type": "page_error", 
      "error_message": "页面渲染失败",
      "error_context": {
        "user_info": {
          "openid": "test_openid_batch_2"
        },
        "page_info": {
          "path": "/pages/resume/create",
          "component": "ResumeForm"
        }
      }
    }
  ]
}
```

**响应格式：**
```json
{
  "success": true,
  "message": "批量错误上报成功，共 2 条",
  "data": {
    "batch_size": 2,
    "timestamp": "2025-07-02T13:47:14.016404"
  }
}
```

## 兼容版接口

### 错误上报（兼容旧版本）

**接口地址：** `POST /resume/error-report`

**请求格式：**
```json
{
  "error_type": "javascript_error",
  "error_message": "兼容性测试错误",
  "error_stack": "Error stack trace...",
  "page_path": "/pages/legacy/test",
  "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)",
  "device_info": {
    "platform": "ios",
    "model": "iPhone 12"
  },
  "app_version": "1.0.0",
  "system_info": {
    "wechat_version": "8.0.20"
  },
  "network_type": "wifi",
  "timestamp": "2025-07-02T13:43:48.577491",
  "openid": "test_openid_legacy"
}
```

**响应格式：**
```json
{
  "message": "错误上报成功"
}
```

## 管理接口（需要认证）

### 1. 获取错误统计

**接口地址：** `GET /error/stats`

**查询参数：**
- `days`: 统计天数（1-30，默认7）
- `limit`: 最近错误数量限制（1-50，默认10）

**响应格式：**
```json
{
  "total_errors": 100,
  "error_types": {
    "miniprogram_error": 45,
    "api_error": 30,
    "page_error": 25
  },
  "recent_errors": [...],
  "time_range": {
    "start_time": "2025-06-25T13:47:14.006631",
    "end_time": "2025-07-02T13:47:14.006631",
    "days": 7
  }
}
```

### 2. 获取错误列表

**接口地址：** `GET /error`

**查询参数：**
- `error_type`: 错误类型筛选（可选）
- `days`: 查询天数（1-30，默认7）
- `limit`: 返回数量限制（1-100，默认20）
- `offset`: 偏移量（默认0）

**响应格式：**
```json
{
  "total": 100,
  "items": [...]
}
```

## 错误类型说明

常见错误类型：
- `miniprogram_error`: 小程序运行时错误
- `api_error`: API请求错误
- `page_error`: 页面渲染错误
- `javascript_error`: JavaScript执行错误
- `network_error`: 网络连接错误
- `auth_error`: 认证相关错误

## 用户信息处理

### 用户ID验证
- 系统会验证`user_id`是否为有效的已存在用户
- 如果`user_id`不存在，会尝试通过`openid`查找用户
- 如果都无法找到有效用户，`user_id`字段将设为`null`

### 数据存储
- 所有动态错误内容存储在`error_context` JSON字段中
- 保留兼容字段以支持旧版本接口
- 用户信息从`error_context`中提取并验证

## 注意事项

1. **无需认证**：错误上报接口不需要认证，避免认证失败导致无法上报错误
2. **容错处理**：即使上报失败也不会抛出异常，确保不影响用户体验
3. **数据验证**：系统会验证用户ID的有效性，无效ID会被忽略
4. **兼容性**：同时支持新旧两套接口格式
5. **灵活存储**：使用JSON字段存储动态错误上下文信息

## 示例代码

### JavaScript (微信小程序)

```javascript
// 简化版错误上报
function reportError(errorType, errorMessage, context = {}) {
  wx.request({
    url: 'https://your-domain.com/error/report',
    method: 'POST',
    data: {
      error_type: errorType,
      error_message: errorMessage,
      error_context: {
        user_info: {
          openid: wx.getStorageSync('openid'),
          user_id: wx.getStorageSync('user_id')
        },
        system_info: wx.getSystemInfoSync(),
        timestamp: new Date().toISOString(),
        ...context
      }
    },
    success: (res) => {
      console.log('错误上报成功:', res.data);
    },
    fail: (err) => {
      console.error('错误上报失败:', err);
    }
  });
}

// 使用示例
reportError('miniprogram_error', 'Cannot read property of undefined', {
  page_path: getCurrentPages().pop().route,
  error_stack: 'Error at line 123'
});
```
