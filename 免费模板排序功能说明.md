# 免费模板排序功能说明

## 概述

为了更好地控制微信端免费模板的显示顺序，我们为系统添加了完整的排序功能。现在支持多种排序方式，包括自定义排序优先级。

## 实现方案

### 1. 数据库层面

#### 新增字段
- 在`free_templates`表中新增`sort_order`字段
- 字段类型：`INT`
- 默认值：`0`
- 约束：`NOT NULL`
- 说明：数值越小越靠前显示

#### 数据库迁移
- 创建了迁移脚本`migrate_add_sort_order_field.py`
- 自动为现有数据设置默认排序值
- 提供迁移前后的数据对比

### 2. API接口增强

#### 新增排序参数
所有模板列表接口现在支持以下排序参数：

**排序字段 (order_by)**：
- `sort_order`：自定义排序优先级（默认）
- `id`：模板ID
- `batch_flag`：批次标识
- `download_count`：下载次数
- `type`：文件类型
- `created_at`：创建时间
- `updated_at`：更新时间

**排序方向 (order_direction)**：
- `asc`：升序（默认，适合sort_order）
- `desc`：降序

#### 接口示例

```bash
# 使用默认排序（按sort_order升序）
GET /free-templates/

# 按下载次数降序排列
GET /free-templates/?order_by=download_count&order_direction=desc

# 按创建时间降序排列
GET /free-templates/?order_by=created_at&order_direction=desc

# 按批次升序排列
GET /free-templates/?order_by=batch_flag&order_direction=asc
```

### 3. 导入脚本支持

#### 新增字段支持
导入脚本现在支持`sort_order`字段的多种列名格式：
- `sort_order`
- `sortOrder`
- `排序`
- `排序优先级`
- `显示顺序`
- `order`

#### 使用示例

**CSV文件格式**：
```csv
id,batch_flag,sort_order,baidu_url,baidu_pass,quark_url,quark_pass,type
blackWhite/01.jpg,blackWhite,1,https://pan.baidu.com/s/xxx,abc1,https://quark.cn/xxx,abc1,word
blackWhite/02.jpg,blackWhite,2,https://pan.baidu.com/s/yyy,abc2,https://quark.cn/yyy,abc2,word
colorful/01.jpg,colorful,3,https://pan.baidu.com/s/zzz,abc3,https://quark.cn/zzz,abc3,word
```

**Excel文件格式**：
可以在Excel中使用中文列名，如"排序优先级"、"显示顺序"等。

## 使用方法

### 1. 数据库迁移

```bash
# 执行数据库迁移，添加sort_order字段
python migrate_add_sort_order_field.py
```

### 2. 设置模板排序

#### 方法一：通过导入脚本
1. 准备包含sort_order字段的CSV或Excel文件
2. 使用导入脚本更新数据

```bash
# 导入包含排序信息的文件
python server_scripts/import_free_templates.py templates_with_order.xlsx
```

#### 方法二：直接修改数据库
```sql
-- 设置特定模板的排序优先级
UPDATE free_templates SET sort_order = 1 WHERE id = 'blackWhite/featured.jpg';
UPDATE free_templates SET sort_order = 2 WHERE id = 'colorful/popular.jpg';

-- 按批次设置排序（黑白模板优先）
UPDATE free_templates SET sort_order = 1 WHERE batch_flag = 'blackWhite';
UPDATE free_templates SET sort_order = 2 WHERE batch_flag = 'colorful';
UPDATE free_templates SET sort_order = 3 WHERE batch_flag = 'modern';
```

### 3. 微信端调用

#### 默认排序（推荐）
```javascript
// 获取按自定义顺序排列的模板
wx.request({
  url: 'https://your-api.com/free-templates/',
  method: 'GET',
  success: function(res) {
    // 模板已按sort_order升序排列
    console.log(res.data.templates);
  }
});
```

#### 自定义排序
```javascript
// 按下载次数排序
wx.request({
  url: 'https://your-api.com/free-templates/?order_by=download_count&order_direction=desc',
  method: 'GET',
  success: function(res) {
    // 模板按下载次数降序排列
    console.log(res.data.templates);
  }
});
```

## 排序策略建议

### 1. 基础排序策略
- **热门模板**：sort_order = 1-10（最优先显示）
- **新模板**：sort_order = 11-50（次优先显示）
- **普通模板**：sort_order = 51-100（正常显示）
- **旧模板**：sort_order = 101+（较后显示）

### 2. 批次排序策略
- **精选批次**：sort_order = 1-100
- **热门批次**：sort_order = 101-200
- **普通批次**：sort_order = 201-300

### 3. 动态排序策略
可以结合下载次数、用户反馈等数据动态调整sort_order值：

```sql
-- 根据下载次数调整排序（每周执行）
UPDATE free_templates 
SET sort_order = CASE 
  WHEN download_count > 100 THEN 1
  WHEN download_count > 50 THEN 2
  WHEN download_count > 10 THEN 3
  ELSE 4
END;
```

## 最佳实践

### 1. 排序字段选择
- **推荐使用sort_order**：提供最大的灵活性
- **备选方案**：download_count（按热度排序）
- **时间排序**：created_at（按新旧排序）

### 2. 微信端实现建议
```javascript
// 在微信端可以提供排序选项
const sortOptions = [
  { key: 'sort_order', name: '推荐排序', direction: 'asc' },
  { key: 'download_count', name: '热度排序', direction: 'desc' },
  { key: 'created_at', name: '最新排序', direction: 'desc' }
];
```

### 3. 性能优化
- sort_order字段已建立索引，查询性能良好
- 建议在微信端缓存排序结果
- 可以考虑分页加载以提高响应速度

## 注意事项

1. **数据一致性**：确保sort_order值的唯一性和连续性
2. **默认值处理**：新导入的模板如果没有指定sort_order，默认为0
3. **排序方向**：sort_order使用升序（asc），数值越小越靠前
4. **批量更新**：大量修改sort_order时建议使用事务
5. **监控排序效果**：定期检查排序是否符合预期

## 总结

通过在**后端查询时调整排序**的方案，我们实现了：

✅ **灵活性高**：支持多种排序方式
✅ **性能优化**：数据库层面排序，效率高
✅ **易于维护**：通过sort_order字段统一管理
✅ **向后兼容**：不影响现有功能
✅ **扩展性强**：可以轻松添加新的排序字段

这种方案比在导入时调整顺序或在微信端调整更加合理和高效。
