#!/usr/bin/env python3
"""
用户行为记录表扩展迁移脚本
添加新字段以支持会员权益操作记录
"""

import pymysql
import logging
import sys
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 数据库配置 - 从环境变量或配置文件获取
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 从DATABASE_URL解析数据库配置
DATABASE_URL = os.getenv("DATABASE_URL", "mysql+pymysql://resume_user:Resume123!@localhost/resume_service")

# 解析数据库URL
# 格式: mysql+pymysql://username:password@host:port/database
import re
url_pattern = r'mysql\+pymysql://([^:]+):([^@]+)@([^:/]+)(?::(\d+))?/(.+)'
match = re.match(url_pattern, DATABASE_URL)

if match:
    username, password, host, port, database = match.groups()
    DB_CONFIG = {
        'host': host,
        'port': int(port) if port else 3306,
        'user': username,
        'password': password,
        'database': database,
        'charset': 'utf8mb4'
    }
else:
    # 默认配置
    DB_CONFIG = {
        'host': 'localhost',
        'port': 3306,
        'user': 'resume_user',
        'password': 'Resume123!',
        'database': 'resume_service',
        'charset': 'utf8mb4'
    }

def get_connection():
    """获取数据库连接"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        logger.info("数据库连接成功")
        return connection
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def check_column_exists(connection, table_name, column_name):
    """检查列是否存在"""
    try:
        cursor = connection.cursor()
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = '{DB_CONFIG['database']}' 
            AND TABLE_NAME = '{table_name}' 
            AND COLUMN_NAME = '{column_name}'
        """)
        result = cursor.fetchone()
        return result[0] > 0
    except Exception as e:
        logger.error(f"检查列是否存在失败: {e}")
        return False

def add_user_action_columns(connection):
    """添加用户行为记录表的新字段"""
    try:
        cursor = connection.cursor()
        
        # 定义要添加的列
        new_columns = [
            ("feature_name", "VARCHAR(50) NULL COMMENT '功能名称，如resume_export, idphoto_generate'"),
            ("resource_type", "VARCHAR(30) NULL COMMENT '资源类型，如pdf, jpeg, idphoto, template'"),
            ("resource_id", "VARCHAR(100) NULL COMMENT '资源ID，可以是文件ID、模板ID等'"),
            ("file_size", "INT NULL COMMENT '文件大小（字节）'"),
            ("file_format", "VARCHAR(20) NULL COMMENT '文件格式，如pdf, jpeg, png'"),
            ("operation_status", "VARCHAR(20) DEFAULT 'completed' COMMENT '操作状态：pending, completed, failed'"),
            ("error_message", "TEXT NULL COMMENT '错误信息（如果操作失败）'"),
            ("is_member_action", "BOOLEAN DEFAULT FALSE COMMENT '是否为会员权益操作'"),
            ("consumed_quota", "INT DEFAULT 1 COMMENT '消耗的配额数量'"),
            ("client_info", "JSON NULL COMMENT '客户端信息，如设备类型、版本等'"),
            ("ip_address", "VARCHAR(45) NULL COMMENT '用户IP地址'"),
            ("updated_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'")
        ]
        
        added_columns = []
        skipped_columns = []
        
        for column_name, column_definition in new_columns:
            if check_column_exists(connection, 'user_actions', column_name):
                logger.info(f"列 {column_name} 已存在，跳过添加")
                skipped_columns.append(column_name)
            else:
                try:
                    sql = f"ALTER TABLE user_actions ADD COLUMN {column_name} {column_definition}"
                    cursor.execute(sql)
                    logger.info(f"成功添加列: {column_name}")
                    added_columns.append(column_name)
                except Exception as e:
                    logger.error(f"添加列 {column_name} 失败: {e}")
                    return False
        
        # 添加索引
        indexes_to_add = [
            ("idx_feature_name", "feature_name"),
            ("idx_resource_type", "resource_type"),
            ("idx_operation_status", "operation_status"),
            ("idx_is_member_action", "is_member_action"),
            ("idx_updated_at", "updated_at")
        ]
        
        for index_name, column_name in indexes_to_add:
            try:
                # 检查索引是否已存在
                cursor.execute(f"""
                    SELECT COUNT(*) 
                    FROM INFORMATION_SCHEMA.STATISTICS 
                    WHERE TABLE_SCHEMA = '{DB_CONFIG['database']}' 
                    AND TABLE_NAME = 'user_actions' 
                    AND INDEX_NAME = '{index_name}'
                """)
                if cursor.fetchone()[0] == 0:
                    cursor.execute(f"CREATE INDEX {index_name} ON user_actions ({column_name})")
                    logger.info(f"成功创建索引: {index_name}")
                else:
                    logger.info(f"索引 {index_name} 已存在，跳过创建")
            except Exception as e:
                logger.warning(f"创建索引 {index_name} 失败: {e}")
        
        connection.commit()
        
        logger.info(f"用户行为记录表扩展完成:")
        logger.info(f"  - 新增列: {len(added_columns)} 个")
        logger.info(f"  - 跳过列: {len(skipped_columns)} 个")
        
        return True
        
    except Exception as e:
        logger.error(f"添加用户行为记录表字段失败: {e}")
        connection.rollback()
        return False

def main():
    """主函数"""
    logger.info("开始用户行为记录表扩展迁移...")
    
    # 获取数据库连接
    connection = get_connection()
    if not connection:
        logger.error("无法连接到数据库，退出")
        sys.exit(1)
    
    try:
        # 添加新字段
        if add_user_action_columns(connection):
            logger.info("用户行为记录表扩展迁移完成")
        else:
            logger.error("用户行为记录表扩展迁移失败")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"迁移过程中发生异常: {e}")
        sys.exit(1)
    finally:
        connection.close()
        logger.info("数据库连接已关闭")

if __name__ == "__main__":
    main()
