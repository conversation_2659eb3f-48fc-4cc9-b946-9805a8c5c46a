#!/usr/bin/env python3
"""
数据备份导出脚本
专门用于导出free_templates和resume_thumbs表数据的备份脚本
"""
import mysql.connector
from mysql.connector import Error
import json
import logging
import sys
import os
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库连接配置
DB_CONFIG = {
    'host': 'localhost',
    'database': 'resume_service',
    'user': 'resume_user',
    'password': 'Resume123!'
}

# 备份目录
BACKUP_DIR = "data_backups"

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        return connection
    except Error as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def ensure_backup_directory():
    """确保备份目录存在"""
    if not os.path.exists(BACKUP_DIR):
        os.makedirs(BACKUP_DIR)
        logger.info(f"创建备份目录: {BACKUP_DIR}")

def export_table_data(connection, table_name, output_file):
    """导出表数据到JSON文件"""
    try:
        cursor = connection.cursor(dictionary=True)
        
        # 查询所有数据
        cursor.execute(f"SELECT * FROM {table_name}")
        rows = cursor.fetchall()
        
        # 处理datetime对象，转换为字符串
        for row in rows:
            for key, value in row.items():
                if isinstance(value, datetime):
                    row[key] = value.isoformat()
        
        # 写入JSON文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({
                'table_name': table_name,
                'export_time': datetime.now().isoformat(),
                'record_count': len(rows),
                'data': rows
            }, f, ensure_ascii=False, indent=2)
        
        cursor.close()
        logger.info(f"成功导出 {table_name} 表数据到 {output_file}，共 {len(rows)} 条记录")
        return len(rows)
        
    except Error as e:
        logger.error(f"导出 {table_name} 表数据失败: {e}")
        return 0

def export_table_sql(connection, table_name, output_file):
    """导出表数据为SQL INSERT语句"""
    try:
        cursor = connection.cursor()
        
        # 获取表结构
        cursor.execute(f"SHOW CREATE TABLE {table_name}")
        create_table_result = cursor.fetchone()
        create_table_sql = create_table_result[1]
        
        # 获取所有数据
        cursor.execute(f"SELECT * FROM {table_name}")
        rows = cursor.fetchall()
        
        # 获取列名
        cursor.execute(f"DESCRIBE {table_name}")
        columns = [col[0] for col in cursor.fetchall()]
        
        # 生成SQL文件
        with open(output_file, 'w', encoding='utf-8') as f:
            # 写入文件头注释
            f.write(f"-- {table_name} 表数据备份\n")
            f.write(f"-- 导出时间: {datetime.now().isoformat()}\n")
            f.write(f"-- 记录数量: {len(rows)}\n\n")
            
            # 写入表结构
            f.write(f"-- 表结构\n")
            f.write(f"DROP TABLE IF EXISTS {table_name};\n")
            f.write(f"{create_table_sql};\n\n")
            
            # 写入数据
            if rows:
                f.write(f"-- 数据插入\n")
                f.write(f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES\n")
                
                for i, row in enumerate(rows):
                    # 处理每个值，确保正确的SQL格式
                    values = []
                    for value in row:
                        if value is None:
                            values.append('NULL')
                        elif isinstance(value, str):
                            # 转义单引号
                            escaped_value = value.replace("'", "''")
                            values.append(f"'{escaped_value}'")
                        elif isinstance(value, (int, float)):
                            values.append(str(value))
                        elif isinstance(value, datetime):
                            values.append(f"'{value.isoformat()}'")
                        else:
                            # 其他类型转为字符串
                            escaped_value = str(value).replace("'", "''")
                            values.append(f"'{escaped_value}'")
                    
                    # 写入INSERT语句
                    if i == len(rows) - 1:
                        f.write(f"({', '.join(values)});\n")
                    else:
                        f.write(f"({', '.join(values)}),\n")
        
        cursor.close()
        logger.info(f"成功导出 {table_name} 表SQL到 {output_file}，共 {len(rows)} 条记录")
        return len(rows)
        
    except Error as e:
        logger.error(f"导出 {table_name} 表SQL失败: {e}")
        return 0

def export_mysqldump(table_name, output_file):
    """使用mysqldump导出表数据"""
    try:
        import subprocess
        
        # 构建mysqldump命令
        cmd = [
            'mysqldump',
            f'--host={DB_CONFIG["host"]}',
            f'--user={DB_CONFIG["user"]}',
            f'--password={DB_CONFIG["password"]}',
            '--single-transaction',
            '--routines',
            '--triggers',
            '--complete-insert',
            '--extended-insert=FALSE',
            DB_CONFIG['database'],
            table_name
        ]
        
        # 执行命令
        with open(output_file, 'w', encoding='utf-8') as f:
            result = subprocess.run(cmd, stdout=f, stderr=subprocess.PIPE, text=True)
        
        if result.returncode == 0:
            logger.info(f"成功使用mysqldump导出 {table_name} 到 {output_file}")
            return True
        else:
            logger.error(f"mysqldump导出失败: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"mysqldump导出异常: {e}")
        return False

def get_table_info(connection, table_name):
    """获取表信息"""
    try:
        cursor = connection.cursor()
        
        # 获取记录数
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        
        # 获取表大小
        cursor.execute(f"""
            SELECT 
                ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'size_mb'
            FROM information_schema.tables 
            WHERE table_schema = '{DB_CONFIG['database']}' 
            AND table_name = '{table_name}'
        """)
        size_result = cursor.fetchone()
        size_mb = size_result[0] if size_result else 0
        
        cursor.close()
        return count, size_mb
        
    except Error as e:
        logger.error(f"获取表 {table_name} 信息失败: {e}")
        return 0, 0

def main():
    """主函数"""
    logger.info("开始数据备份导出...")
    
    # 确保备份目录存在
    ensure_backup_directory()
    
    # 获取数据库连接
    connection = get_db_connection()
    if not connection:
        logger.error("无法连接到数据库，导出失败")
        sys.exit(1)
    
    try:
        # 需要备份的表
        tables_to_backup = ['free_templates', 'resume_thumbs']
        
        # 生成时间戳
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        backup_summary = {
            'export_time': datetime.now().isoformat(),
            'tables': {}
        }
        
        for table_name in tables_to_backup:
            logger.info(f"开始备份表: {table_name}")
            
            # 获取表信息
            record_count, size_mb = get_table_info(connection, table_name)
            logger.info(f"表 {table_name}: {record_count} 条记录, {size_mb} MB")
            
            if record_count == 0:
                logger.warning(f"表 {table_name} 没有数据，跳过备份")
                continue
            
            # 定义输出文件名
            json_file = os.path.join(BACKUP_DIR, f"{table_name}_{timestamp}.json")
            sql_file = os.path.join(BACKUP_DIR, f"{table_name}_{timestamp}.sql")
            dump_file = os.path.join(BACKUP_DIR, f"{table_name}_{timestamp}_dump.sql")
            
            # 导出JSON格式
            json_count = export_table_data(connection, table_name, json_file)
            
            # 导出SQL格式
            sql_count = export_table_sql(connection, table_name, sql_file)
            
            # 使用mysqldump导出（如果可用）
            dump_success = export_mysqldump(table_name, dump_file)
            
            # 记录备份信息
            backup_summary['tables'][table_name] = {
                'record_count': record_count,
                'size_mb': size_mb,
                'json_file': json_file if json_count > 0 else None,
                'sql_file': sql_file if sql_count > 0 else None,
                'dump_file': dump_file if dump_success else None,
                'backup_success': json_count > 0 or sql_count > 0 or dump_success
            }
        
        # # 生成备份摘要文件
        # summary_file = os.path.join(BACKUP_DIR, f"backup_summary_{timestamp}.json")
        # with open(summary_file, 'w', encoding='utf-8') as f:
            
        #     json.dump(backup_summary, f, ensure_ascii=False, indent=2)
        
        # logger.info("=" * 60)
        # logger.info("数据备份导出完成！")
        # logger.info(f"备份目录: {BACKUP_DIR}")
        # logger.info(f"备份摘要: {summary_file}")
        
        # 显示备份结果
        for table_name, info in backup_summary['tables'].items():
            logger.info(f"表 {table_name}:")
            logger.info(f"  - 记录数: {info['record_count']}")
            logger.info(f"  - 大小: {info['size_mb']} MB")
            logger.info(f"  - JSON文件: {info['json_file']}")
            logger.info(f"  - SQL文件: {info['sql_file']}")
            logger.info(f"  - Dump文件: {info['dump_file']}")
            logger.info(f"  - 备份状态: {'成功' if info['backup_success'] else '失败'}")
        
        logger.info("=" * 60)
        
    except Exception as e:
        logger.exception("备份过程中发生异常")
        sys.exit(1)
    
    finally:
        if connection.is_connected():
            connection.close()
            logger.info("数据库连接已关闭")

if __name__ == "__main__":
    main()
