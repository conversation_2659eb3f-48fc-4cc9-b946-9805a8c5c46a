#!/usr/bin/env python3
"""
数据导入脚本
用于将备份数据导入到生产环境的脚本
支持JSON和SQL格式的数据导入
"""
import mysql.connector
from mysql.connector import Error
import json
import logging
import sys
import os
import argparse
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库连接配置
DB_CONFIG = {
    'host': 'localhost',
    'database': 'resume_service',
    'user': 'resume_user',
    'password': 'Resume123!'
}

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        return connection
    except Error as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def check_table_exists(connection, table_name):
    """检查表是否存在"""
    try:
        cursor = connection.cursor()
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = '{DB_CONFIG['database']}' 
            AND table_name = '{table_name}'
        """)
        result = cursor.fetchone()
        cursor.close()
        return result[0] > 0
    except Error as e:
        logger.error(f"检查表存在性失败: {e}")
        return False

def get_table_record_count(connection, table_name):
    """获取表记录数"""
    try:
        cursor = connection.cursor()
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        cursor.close()
        return count
    except Error as e:
        logger.error(f"获取表 {table_name} 记录数失败: {e}")
        return 0

def clear_table_data(connection, table_name, confirm=False):
    """清空表数据"""
    if not confirm:
        response = input(f"确定要清空表 {table_name} 的所有数据吗？(yes/no): ")
        if response.lower() != 'yes':
            logger.info("用户取消清空操作")
            return False
    
    try:
        cursor = connection.cursor()
        
        # 禁用外键检查
        cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
        
        # 清空表数据
        cursor.execute(f"DELETE FROM {table_name}")
        
        # 重置自增ID（如果有的话）
        try:
            cursor.execute(f"ALTER TABLE {table_name} AUTO_INCREMENT = 1")
        except:
            pass  # 如果没有自增字段，忽略错误
        
        # 重新启用外键检查
        cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
        
        connection.commit()
        cursor.close()
        logger.info(f"成功清空表 {table_name}")
        return True
        
    except Error as e:
        logger.error(f"清空表 {table_name} 失败: {e}")
        connection.rollback()
        return False

def import_json_data(connection, json_file, table_name=None, clear_existing=False):
    """从JSON文件导入数据"""
    try:
        # 读取JSON文件
        with open(json_file, 'r', encoding='utf-8') as f:
            backup_data = json.load(f)
        
        # 获取表名和数据
        target_table = table_name or backup_data.get('table_name')
        if not target_table:
            logger.error("无法确定目标表名")
            return False
        
        data_records = backup_data.get('data', [])
        if not data_records:
            logger.warning("JSON文件中没有数据记录")
            return True
        
        # 检查表是否存在
        if not check_table_exists(connection, target_table):
            logger.error(f"目标表 {target_table} 不存在")
            return False
        
        # 清空现有数据（如果需要）
        if clear_existing:
            if not clear_table_data(connection, target_table, confirm=True):
                return False
        
        cursor = connection.cursor()
        
        # 获取表结构
        cursor.execute(f"DESCRIBE {target_table}")
        columns_info = cursor.fetchall()
        column_names = [col[0] for col in columns_info]
        
        # 准备插入语句
        placeholders = ', '.join(['%s'] * len(column_names))
        insert_sql = f"INSERT INTO {target_table} ({', '.join(column_names)}) VALUES ({placeholders})"
        
        # 批量插入数据
        inserted_count = 0
        failed_count = 0
        
        for record in data_records:
            try:
                # 按列顺序准备值
                values = []
                for col_name in column_names:
                    value = record.get(col_name)
                    # 处理datetime字符串
                    if value and isinstance(value, str) and 'T' in value:
                        try:
                            # 尝试解析ISO格式的datetime
                            datetime.fromisoformat(value.replace('Z', '+00:00'))
                        except:
                            pass  # 如果不是datetime格式，保持原值
                    values.append(value)
                
                cursor.execute(insert_sql, values)
                inserted_count += 1
                
            except Error as e:
                logger.warning(f"插入记录失败: {e}, 记录: {record}")
                failed_count += 1
                continue
        
        connection.commit()
        cursor.close()
        
        logger.info(f"JSON数据导入完成: 成功 {inserted_count} 条，失败 {failed_count} 条")
        return True
        
    except Exception as e:
        logger.error(f"导入JSON数据失败: {e}")
        connection.rollback()
        return False

def import_sql_file(connection, sql_file):
    """从SQL文件导入数据"""
    try:
        # 读取SQL文件
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 分割SQL语句
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        cursor = connection.cursor()
        
        # 禁用外键检查
        cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
        
        executed_count = 0
        failed_count = 0
        
        for sql_statement in sql_statements:
            # 跳过注释行
            if sql_statement.startswith('--') or sql_statement.startswith('/*'):
                continue
            
            try:
                cursor.execute(sql_statement)
                executed_count += 1
            except Error as e:
                logger.warning(f"执行SQL语句失败: {e}")
                logger.warning(f"SQL: {sql_statement[:100]}...")
                failed_count += 1
                continue
        
        # 重新启用外键检查
        cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
        
        connection.commit()
        cursor.close()
        
        logger.info(f"SQL文件导入完成: 成功执行 {executed_count} 条语句，失败 {failed_count} 条")
        return True
        
    except Exception as e:
        logger.error(f"导入SQL文件失败: {e}")
        connection.rollback()
        return False

def import_mysqldump_file(sql_file):
    """使用mysql命令导入mysqldump文件"""
    try:
        import subprocess
        
        # 构建mysql命令
        cmd = [
            'mysql',
            f'--host={DB_CONFIG["host"]}',
            f'--user={DB_CONFIG["user"]}',
            f'--password={DB_CONFIG["password"]}',
            DB_CONFIG['database']
        ]
        
        # 执行命令
        with open(sql_file, 'r', encoding='utf-8') as f:
            result = subprocess.run(cmd, stdin=f, stderr=subprocess.PIPE, text=True)
        
        if result.returncode == 0:
            logger.info(f"成功使用mysql命令导入 {sql_file}")
            return True
        else:
            logger.error(f"mysql命令导入失败: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"mysql命令导入异常: {e}")
        return False

def list_backup_files(backup_dir="data_backups"):
    """列出可用的备份文件"""
    if not os.path.exists(backup_dir):
        logger.error(f"备份目录 {backup_dir} 不存在")
        return []
    
    backup_files = []
    for file in os.listdir(backup_dir):
        if file.endswith(('.json', '.sql')):
            file_path = os.path.join(backup_dir, file)
            file_size = os.path.getsize(file_path)
            backup_files.append({
                'file': file,
                'path': file_path,
                'size': file_size,
                'type': 'JSON' if file.endswith('.json') else 'SQL'
            })
    
    return sorted(backup_files, key=lambda x: x['file'])

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='数据导入脚本')
    parser.add_argument('--file', '-f', help='要导入的备份文件路径')
    parser.add_argument('--table', '-t', help='目标表名（可选，JSON文件会自动检测）')
    parser.add_argument('--clear', '-c', action='store_true', help='导入前清空目标表')
    parser.add_argument('--list', '-l', action='store_true', help='列出可用的备份文件')
    parser.add_argument('--backup-dir', '-d', default='data_backups', help='备份文件目录')
    parser.add_argument('--format', choices=['json', 'sql', 'dump'], help='强制指定文件格式')
    
    args = parser.parse_args()
    
    # 列出备份文件
    if args.list:
        backup_files = list_backup_files(args.backup_dir)
        if not backup_files:
            logger.info("没有找到备份文件")
            return
        
        logger.info("可用的备份文件:")
        logger.info("-" * 80)
        logger.info(f"{'文件名':<40} {'类型':<8} {'大小':<15}")
        logger.info("-" * 80)
        for file_info in backup_files:
            size_str = f"{file_info['size'] / 1024:.1f} KB"
            logger.info(f"{file_info['file']:<40} {file_info['type']:<8} {size_str:<15}")
        return
    
    # 检查文件参数
    if not args.file:
        logger.error("请指定要导入的文件路径，使用 --file 参数")
        logger.info("使用 --list 查看可用的备份文件")
        sys.exit(1)
    
    # 检查文件是否存在
    if not os.path.exists(args.file):
        logger.error(f"文件不存在: {args.file}")
        sys.exit(1)
    
    logger.info("开始数据导入...")
    logger.info(f"源文件: {args.file}")
    logger.info(f"目标表: {args.table or '自动检测'}")
    logger.info(f"清空现有数据: {'是' if args.clear else '否'}")
    
    # 获取数据库连接
    connection = get_db_connection()
    if not connection:
        logger.error("无法连接到数据库，导入失败")
        sys.exit(1)
    
    try:
        # 确定文件格式
        file_format = args.format
        if not file_format:
            if args.file.endswith('.json'):
                file_format = 'json'
            elif 'dump' in args.file:
                file_format = 'dump'
            else:
                file_format = 'sql'
        
        # 执行导入
        success = False
        if file_format == 'json':
            success = import_json_data(connection, args.file, args.table, args.clear)
        elif file_format == 'dump':
            success = import_mysqldump_file(args.file)
        else:
            success = import_sql_file(connection, args.file)
        
        if success:
            logger.info("=" * 60)
            logger.info("数据导入完成！")
            
            # 显示导入后的表状态
            if args.table:
                count = get_table_record_count(connection, args.table)
                logger.info(f"表 {args.table} 当前记录数: {count}")
            
            logger.info("=" * 60)
        else:
            logger.error("数据导入失败")
            sys.exit(1)
        
    except Exception as e:
        logger.exception("导入过程中发生异常")
        sys.exit(1)
    
    finally:
        if connection.is_connected():
            connection.close()
            logger.info("数据库连接已关闭")

if __name__ == "__main__":
    main()
