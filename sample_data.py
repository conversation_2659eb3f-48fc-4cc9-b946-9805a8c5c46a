from app.schemas.resume import ResumeData, BasicInfo, JobIntention, EducationItem, WorkItem, ProjectItem, SchoolExperienceItem




resume_data = {
  "moduleOrders": {
    "basicInfo": 0,
    "jobIntention": 1,
    "education": 2,
    "school": 3,
    "internship": 4,
    "work": 5,
    "project": 6,
    "skills": 7,
    "awards": 8,
    "interests": 9,
    "evaluation": 10,
    "custom1": 11,
    "custom2": 12,
    "custom3": 13
  },
  "basicInfo": {
    "name": "李华",
    "gender": "女",
    "phone": "13912345678",
    "photoUrl": """data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQNDAsLDBkS+woGT3P4UUUAh1FFFACN2oXvRRQCG0UUUCHGhe9FFADaKKKAHL3phoooAWgfpRRQAppooooGxaKKKBBSHjNFFDGhaCCDggg+9FFIAowfSiigA57CjB9KKKaBiCloopAhKKKKaBiUUUUIQU7JPWiigbEpaKKBCGmmiigBKKKKAP/Z""",
    "city": "上海",
    "email": "<EMAIL>",
    "wechat": "LiHua_2023",
    "age": "28",
    "birthday": "1995-05-20",
    "marriage": "未婚",
    "politics": "群众",
    "nation": "汉族",
    "hometown": "浙江杭州",
    "height": "165cm",
    "weight": "52kg",
    "educationLevel": "硕士",
    "customTitle1": "技术博客",
    "customContent1": "https://lihua-tech.blog",
    "customTitle2": "开源项目",
    "customContent2": "GitHub: lihua-dev"
  },
  "jobIntention": {
    "position": "高级前端开发工程师",
    "city": "上海",
    "salary": "25k-35k",
    "status": "在职，寻求更好机会"
  },
  "education": [
    {
      "school": "复旦大学",
      "major": "软件工程",
      "degree": "硕士",
      "startDate": "2018-09",
      "endDate": "2021-06",
      "description": "研究方向：前端框架优化，发表2篇核心期刊论文"
    },
    {
      "school": "浙江大学",
      "major": "计算机科学与技术",
      "degree": "学士",
      "startDate": "2014-09",
      "endDate": "2018-06",
      "description": "GPA 3.8/4.0，ACM校队成员"
    }
  ],
  "school": [
    {
      "role": "学生会主席",
      "startDate": "2016-09",
      "endDate": "2017-06",
      "content": "组织校级技术竞赛，参与人数超过500人"
    },
    {
      "role": "学生会主席2",
      "startDate": "2016-09",
      "endDate": "2017-06",
      "content": "组织校级技术竞赛，参与人数超过500人"
    }
  ],
  "internship": [
    {
      "company": "腾讯科技",
      "position": "前端开发实习生",
      "startDate": "2020-07",
      "endDate": "2020-12",
      "content": "参与微信小程序性能优化项目，首屏加载速度提升40%"
    },
    {
      "company": "腾讯科技2",
      "position": "前端开发实习生",
      "startDate": "2020-07",
      "endDate": "2020-12",
      "content": "参与微信小程序性能优化项目，首屏加载速度提升40%"
    },
  ],
  "work": [
    {
      "company": "阿里巴巴集团",
      "position": "前端开发工程师",
      "startDate": "2021-07",
      "endDate": "至今",
      "description": "负责电商中台前端架构设计，主导Vue3+TypeScript技术栈落地"
    },
    {
      "company": "阿里巴巴集团-2",
      "position": "前端开发工程师",
      "startDate": "2021-07",
      "endDate": "至今",
      "description": "负责电商中台前端架构设计，主导Vue3+TypeScript技术栈落地"
    },
  ],
  "project": [
    {
      "projectName": "跨平台电商系统",
      "role": "技术负责人",
      "startDate": "2022-03",
      "endDate": "2023-01",
      "description": "基于Flutter+Node.js的跨平台解决方案，日活用户突破100万"
    },
    {
      "projectName": "跨平台电商系统-2",
      "role": "技术负责人",
      "startDate": "2022-03",
      "endDate": "2023-01",
      "description": "基于Flutter+Node.js的跨平台解决方案，日活用户突破100万"
    },
  ],
  "skills": [
    "React/Vue", "TypeScript", "Webpack",
    "Node.js", "Python", "Docker",
    "Jenkins", "GitLab CI/CD"
  ],
  "awards": [
    "2022年阿里技术卓越奖",
    "2021年QCon优秀演讲者",
    "2020年全国大学生计算机设计大赛一等奖"
  ],
  "interests": [
    "开源社区", "AI技术研究",
    "马拉松", "古典音乐"
  ],
  "evaluation": [
    {
      "content": "技术视野广阔，具备复杂系统架构能力, \n团队管理经验丰富，培养多名中级工程师",
    }
  ],
  "custom1": [
    {
      "customName": "专利信息",
      "startDate": "2021-05",
      "endDate": "2021-05",
      "role": "发明人",
      "content": "一种前端资源懒加载方法（专利号：ZL2021XXXXXXX）"
    }
  ],
  "custom2": [
    {
      "customName": "职业认证",
      "startDate": "2022-10",
      "endDate": "2025-10",
      "role": "持证者",
      "content": "AWS Certified Solutions Architect"
    }
  ],
  "custom3": [
    {
      "customName": "技术培训",
      "startDate": "2023-01",
      "endDate": "2023-03",
      "role": "讲师",
      "content": "Vue3高级特性培训（参与人数：120人）"
    }
  ]
}