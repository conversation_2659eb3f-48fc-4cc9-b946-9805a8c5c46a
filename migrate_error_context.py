#!/usr/bin/env python3
"""
数据库迁移脚本：为错误上报表添加error_context字段
"""

import mysql.connector
from mysql.connector import Error
import json

def migrate_error_context():
    """为错误上报表添加error_context字段"""
    
    # 数据库连接配置
    config = {
        'host': 'localhost',
        'database': 'resume_service',
        'user': 'resume_user',
        'password': 'Resume123!',
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }
    
    connection = None
    cursor = None
    
    try:
        # 连接数据库
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        print("✅ 数据库连接成功")
        
        # 检查error_context字段是否已存在
        check_column_sql = """
        SELECT COUNT(*) 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'resume_service' 
        AND TABLE_NAME = 'error_reports' 
        AND COLUMN_NAME = 'error_context'
        """
        
        cursor.execute(check_column_sql)
        column_exists = cursor.fetchone()[0] > 0
        
        if column_exists:
            print("⚠️  error_context字段已存在，跳过添加")
        else:
            # 添加error_context字段
            add_column_sql = """
            ALTER TABLE error_reports 
            ADD COLUMN error_context JSON NULL COMMENT '错误上下文信息（包含所有动态内容）'
            AFTER error_message
            """
            
            cursor.execute(add_column_sql)
            print("✅ 成功添加error_context字段")
        
        # 检查是否需要调整timestamp字段为可空
        check_timestamp_sql = """
        SELECT IS_NULLABLE 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'resume_service' 
        AND TABLE_NAME = 'error_reports' 
        AND COLUMN_NAME = 'timestamp'
        """
        
        cursor.execute(check_timestamp_sql)
        result = cursor.fetchone()
        if result and result[0] == 'NO':
            # 修改timestamp字段为可空（兼容性）
            modify_timestamp_sql = """
            ALTER TABLE error_reports 
            MODIFY COLUMN timestamp TIMESTAMP NULL COMMENT '错误发生时间（兼容字段）'
            """
            
            cursor.execute(modify_timestamp_sql)
            print("✅ 成功修改timestamp字段为可空")
        
        # 提交更改
        connection.commit()
        print("✅ 数据库迁移完成")
        
        # 显示表结构
        show_structure_sql = "DESCRIBE error_reports"
        cursor.execute(show_structure_sql)
        columns = cursor.fetchall()
        
        print("\n📋 当前表结构:")
        print("字段名\t\t类型\t\t\t可空\t键\t默认值\t\t备注")
        print("-" * 80)
        for column in columns:
            field, type_info, null, key, default, extra = column
            print(f"{field:<15} {type_info:<20} {null:<5} {key:<5} {str(default):<15} {extra}")
        
    except Error as e:
        print(f"❌ 数据库错误: {e}")
        if connection:
            connection.rollback()
        return False
        
    except Exception as e:
        print(f"❌ 执行错误: {e}")
        if connection:
            connection.rollback()
        return False
        
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()
        print("🔌 数据库连接已关闭")
    
    return True

if __name__ == "__main__":
    print("🚀 开始执行错误上报表迁移...")
    success = migrate_error_context()
    if success:
        print("🎉 迁移成功完成！")
    else:
        print("💥 迁移失败！")
