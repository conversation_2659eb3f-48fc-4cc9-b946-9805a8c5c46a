"""
微信支付配置
"""
import os
from typing import Optional
from pydantic import BaseSettings, Field


class WeChatPaySettings(BaseSettings):
    """微信支付配置"""
    
    # ===========================================
    # 微信支付基础配置
    # ===========================================
    WECHAT_PAY_APPID: str = Field(
        default=os.getenv("WECHAT_PAY_APPID", ""),
        description="微信小程序AppID"
    )
    
    WECHAT_PAY_MCHID: str = Field(
        default=os.getenv("WECHAT_PAY_MCHID", ""),
        description="微信支付商户号"
    )
    
    WECHAT_PAY_API_KEY: str = Field(
        default=os.getenv("WECHAT_PAY_API_KEY", ""),
        description="微信支付API密钥"
    )
    
    WECHAT_PAY_API_V3_KEY: str = Field(
        default=os.getenv("WECHAT_PAY_API_V3_KEY", ""),
        description="微信支付APIv3密钥"
    )
    
    # ===========================================
    # 证书配置
    # ===========================================
    WECHAT_PAY_CERT_PATH: str = Field(
        default=os.getenv("WECHAT_PAY_CERT_PATH", "certs/apiclient_cert.pem"),
        description="API证书路径"
    )
    
    WECHAT_PAY_KEY_PATH: str = Field(
        default=os.getenv("WECHAT_PAY_KEY_PATH", "certs/apiclient_key.pem"),
        description="API证书私钥路径"
    )
    
    WECHAT_PAY_CERT_SERIAL_NO: str = Field(
        default=os.getenv("WECHAT_PAY_CERT_SERIAL_NO", ""),
        description="证书序列号"
    )
    
    # ===========================================
    # 回调配置
    # ===========================================
    WECHAT_PAY_NOTIFY_URL: str = Field(
        default=os.getenv("WECHAT_PAY_NOTIFY_URL", "https://your-domain.com/payment/callback"),
        description="支付回调URL"
    )
    
    # ===========================================
    # 环境配置
    # ===========================================
    WECHAT_PAY_SANDBOX: bool = Field(
        default=os.getenv("WECHAT_PAY_SANDBOX", "false").lower() == "true",
        description="是否使用沙箱环境"
    )
    
    # ===========================================
    # API地址配置
    # ===========================================
    WECHAT_PAY_API_BASE_URL: str = Field(
        default="https://api.mch.weixin.qq.com",
        description="微信支付API基础URL"
    )
    
    # ===========================================
    # 订单配置
    # ===========================================
    ORDER_EXPIRE_MINUTES: int = Field(
        default=30,
        description="订单过期时间(分钟)"
    )
    
    ORDER_PREFIX: str = Field(
        default="RS",
        description="订单号前缀"
    )
    
    # ===========================================
    # 支付配置
    # ===========================================
    PAYMENT_TIMEOUT_SECONDS: int = Field(
        default=30,
        description="支付请求超时时间(秒)"
    )
    
    MAX_RETRY_TIMES: int = Field(
        default=3,
        description="支付请求最大重试次数"
    )
    
    # ===========================================
    # 安全配置
    # ===========================================
    SIGNATURE_ALGORITHM: str = Field(
        default="WECHATPAY2-SHA256-RSA2048",
        description="签名算法"
    )
    
    ENCRYPT_ALGORITHM: str = Field(
        default="AEAD_AES_256_GCM",
        description="加密算法"
    )
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 创建配置实例
wechat_pay_settings = WeChatPaySettings()


def validate_wechat_pay_config():
    """验证微信支付配置"""
    errors = []
    
    # 检查必需的配置项
    required_fields = [
        ("WECHAT_PAY_APPID", wechat_pay_settings.WECHAT_PAY_APPID),
        ("WECHAT_PAY_MCHID", wechat_pay_settings.WECHAT_PAY_MCHID),
        ("WECHAT_PAY_API_V3_KEY", wechat_pay_settings.WECHAT_PAY_API_V3_KEY),
        ("WECHAT_PAY_CERT_SERIAL_NO", wechat_pay_settings.WECHAT_PAY_CERT_SERIAL_NO),
        ("WECHAT_PAY_NOTIFY_URL", wechat_pay_settings.WECHAT_PAY_NOTIFY_URL),
    ]
    
    for field_name, field_value in required_fields:
        if not field_value:
            errors.append(f"缺少必需的配置项: {field_name}")
    
    # 检查证书文件
    cert_files = [
        ("WECHAT_PAY_CERT_PATH", wechat_pay_settings.WECHAT_PAY_CERT_PATH),
        ("WECHAT_PAY_KEY_PATH", wechat_pay_settings.WECHAT_PAY_KEY_PATH),
    ]
    
    for field_name, file_path in cert_files:
        if not os.path.exists(file_path):
            errors.append(f"证书文件不存在: {field_name} = {file_path}")
    
    # 检查回调URL格式
    if not wechat_pay_settings.WECHAT_PAY_NOTIFY_URL.startswith(("http://", "https://")):
        errors.append("WECHAT_PAY_NOTIFY_URL 必须是有效的HTTP/HTTPS URL")
    
    if errors:
        error_msg = "微信支付配置验证失败:\n" + "\n".join(f"- {error}" for error in errors)
        raise ValueError(error_msg)
    
    return True


def get_wechat_pay_config():
    """获取微信支付配置"""
    return {
        "appid": wechat_pay_settings.WECHAT_PAY_APPID,
        "mchid": wechat_pay_settings.WECHAT_PAY_MCHID,
        "api_key": wechat_pay_settings.WECHAT_PAY_API_KEY,
        "api_v3_key": wechat_pay_settings.WECHAT_PAY_API_V3_KEY,
        "cert_path": wechat_pay_settings.WECHAT_PAY_CERT_PATH,
        "key_path": wechat_pay_settings.WECHAT_PAY_KEY_PATH,
        "cert_serial_no": wechat_pay_settings.WECHAT_PAY_CERT_SERIAL_NO,
        "notify_url": wechat_pay_settings.WECHAT_PAY_NOTIFY_URL,
        "sandbox": wechat_pay_settings.WECHAT_PAY_SANDBOX,
        "api_base_url": wechat_pay_settings.WECHAT_PAY_API_BASE_URL,
        "order_expire_minutes": wechat_pay_settings.ORDER_EXPIRE_MINUTES,
        "order_prefix": wechat_pay_settings.ORDER_PREFIX,
        "payment_timeout": wechat_pay_settings.PAYMENT_TIMEOUT_SECONDS,
        "max_retry_times": wechat_pay_settings.MAX_RETRY_TIMES,
        "signature_algorithm": wechat_pay_settings.SIGNATURE_ALGORITHM,
        "encrypt_algorithm": wechat_pay_settings.ENCRYPT_ALGORITHM,
    }


# 环境变量示例
ENV_EXAMPLE = """
# 微信支付配置示例
WECHAT_PAY_APPID=wx1234567890abcdef
WECHAT_PAY_MCHID=1234567890
WECHAT_PAY_API_KEY=your_api_key_here
WECHAT_PAY_API_V3_KEY=your_api_v3_key_here
WECHAT_PAY_CERT_PATH=certs/apiclient_cert.pem
WECHAT_PAY_KEY_PATH=certs/apiclient_key.pem
WECHAT_PAY_CERT_SERIAL_NO=1234567890ABCDEF1234567890ABCDEF12345678
WECHAT_PAY_NOTIFY_URL=https://your-domain.com/payment/callback
WECHAT_PAY_SANDBOX=false
"""
