# 错误上报接口文档

## 概述

微信小程序错误上报系统提供了完整的错误收集、存储和查询功能，帮助开发者及时发现和解决应用中的问题。

## 接口列表

### 1. 单个错误上报

**接口地址：** `POST /resume/error-report`

**认证要求：** 无需认证（避免认证失败导致无法上报错误）

**请求参数：**

```json
{
  "error_type": "javascript_error",
  "error_message": "TypeError: Cannot read property 'length' of undefined",
  "error_stack": "TypeError: Cannot read property 'length' of undefined\n    at Object.getLength (app.js:123:45)",
  "page_path": "/pages/resume/create",
  "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15",
  "device_info": {
    "platform": "ios",
    "model": "iPhone 12",
    "system": "iOS 14.7.1",
    "screen_width": 390,
    "screen_height": 844
  },
  "app_version": "1.0.0",
  "system_info": {
    "wechat_version": "8.0.20",
    "sdk_version": "2.19.4"
  },
  "network_type": "wifi",
  "timestamp": "2025-07-02T11:50:31.083254",
  "openid": "test_openid_123456"
}
```

**错误类型枚举：**
- `javascript_error` - JavaScript错误
- `network_error` - 网络错误
- `api_error` - API接口错误
- `render_error` - 渲染错误
- `permission_error` - 权限错误
- `wechat_api_error` - 微信API错误
- `unknown_error` - 未知错误

**响应示例：**

```json
{
  "message": "错误上报成功"
}
```

### 2. 批量错误上报

**接口地址：** `POST /resume/error-report/batch`

**认证要求：** 无需认证

**请求参数：**

```json
{
  "errors": [
    {
      "error_type": "javascript_error",
      "error_message": "测试错误消息 1",
      "error_stack": "Error stack trace 1",
      "page_path": "/pages/test/1",
      "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15",
      "device_info": {
        "platform": "ios",
        "model": "iPhone 12",
        "system": "iOS 14.7.1"
      },
      "app_version": "1.0.0",
      "system_info": {
        "wechat_version": "8.0.20"
      },
      "network_type": "4g",
      "timestamp": "2025-07-02T11:50:32.111177",
      "openid": "test_openid_1"
    }
  ],
  "batch_size": 1,
  "timestamp": "2025-07-02T11:50:32.111177"
}
```

**响应示例：**

```json
{
  "message": "批量错误上报成功，共 3 条"
}
```

### 3. 错误统计查询（需要认证）

**接口地址：** `GET /resume/error-report/stats`

**认证要求：** 需要Bearer Token认证

**查询参数：**
- `days` (可选): 统计天数，默认7天，范围1-30
- `limit` (可选): 最近错误数量限制，默认10条，范围1-50

**响应示例：**

```json
{
  "total_errors": 15,
  "error_types": {
    "javascript_error": 8,
    "network_error": 4,
    "api_error": 3
  },
  "recent_errors": [
    {
      "id": 1,
      "user_id": null,
      "openid": "test_openid_123456",
      "error_type": "javascript_error",
      "error_message": "TypeError: Cannot read property 'length' of undefined",
      "error_stack": "TypeError: Cannot read property 'length' of undefined\n    at Object.getLength (app.js:123:45)",
      "page_path": "/pages/resume/create",
      "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15",
      "device_info": {
        "platform": "ios",
        "model": "iPhone 12",
        "system": "iOS 14.7.1",
        "screen_width": 390,
        "screen_height": 844
      },
      "app_version": "1.0.0",
      "system_info": {
        "wechat_version": "8.0.20",
        "sdk_version": "2.19.4"
      },
      "network_type": "wifi",
      "timestamp": "2025-07-02T11:50:31.083254",
      "created_at": "2025-07-02T11:50:31"
    }
  ],
  "time_range": {
    "start_time": "2025-06-25T11:50:33.127000",
    "end_time": "2025-07-02T11:50:33.127000",
    "days": 7
  }
}
```

### 4. 错误报告列表（需要认证）

**接口地址：** `GET /resume/error-report`

**认证要求：** 需要Bearer Token认证

**查询参数：**
- `error_type` (可选): 错误类型筛选
- `days` (可选): 查询天数，默认7天，范围1-30
- `limit` (可选): 返回数量限制，默认20条，范围1-100
- `offset` (可选): 偏移量，默认0

**响应示例：**

```json
{
  "total": 4,
  "items": [
    {
      "id": 4,
      "user_id": null,
      "openid": "test_openid_3",
      "error_type": "api_error",
      "error_message": "测试错误消息 3",
      "error_stack": "Error stack trace 3",
      "page_path": "/pages/test/3",
      "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15",
      "device_info": {
        "platform": "ios",
        "model": "iPhone 12",
        "system": "iOS 14.7.1"
      },
      "app_version": "1.0.0",
      "system_info": {
        "wechat_version": "8.0.20"
      },
      "network_type": "4g",
      "timestamp": "2025-07-02T11:50:32.111197",
      "created_at": "2025-07-02T11:50:32"
    }
  ]
}
```

## 微信端使用示例

### JavaScript代码示例

```javascript
// 单个错误上报
function reportError(errorData) {
  console.log('📤 上报错误信息:', errorData.error_type);
  
  return request.request({
    url: apiConfig.errorReportUrl,
    method: 'POST',
    data: errorData,
    showLoading: false,
    showError: false,
    needAuth: false, // 错误上报不需要认证
    timeout: 5000
  });
}

// 批量错误上报
function reportErrorBatch(errorList) {
  console.log('📤 批量上报错误信息:', errorList.length, '条');
  
  return request.request({
    url: apiConfig.errorReportUrl + '/batch',
    method: 'POST',
    data: {
      errors: errorList,
      batch_size: errorList.length,
      timestamp: new Date().toISOString()
    },
    showLoading: false,
    showError: false,
    needAuth: false,
    timeout: 10000
  });
}

// 获取错误统计（需要认证）
function getErrorStats() {
  return request.request({
    url: apiConfig.errorReportUrl + '/stats',
    method: 'GET',
    showLoading: false,
    showError: false,
    needAuth: true,
    timeout: 5000
  });
}
```

## 数据库表结构

错误上报数据存储在 `error_reports` 表中，包含以下字段：

- `id`: 主键ID
- `user_id`: 用户ID（可为空，未登录用户）
- `openid`: 微信openid（可为空）
- `error_type`: 错误类型
- `error_message`: 错误信息
- `error_stack`: 错误堆栈
- `page_path`: 发生错误的页面路径
- `user_agent`: 用户代理信息
- `device_info`: 设备信息（JSON格式）
- `app_version`: 应用版本
- `system_info`: 系统信息（JSON格式）
- `network_type`: 网络类型
- `timestamp`: 错误发生时间
- `created_at`: 上报时间

## 注意事项

1. **无认证设计**: 错误上报接口不需要认证，确保即使在认证失败的情况下也能正常上报错误
2. **容错处理**: 错误上报失败不会抛出异常，避免影响用户正常使用
3. **数据完整性**: 支持未登录用户的错误上报，通过openid关联用户信息
4. **性能优化**: 批量上报接口支持一次性提交多个错误，减少网络请求
5. **查询权限**: 统计和列表查询接口需要认证，保护敏感数据
