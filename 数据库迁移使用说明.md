# 数据库迁移使用说明

## 概述

本文档详细说明了如何使用提供的脚本将微信小程序后端服务的数据库迁移到生产环境。

## 脚本文件说明

### 1. 核心迁移脚本

| 脚本文件 | 功能描述 |
|---------|---------|
| `migrate_complete_database.py` | 完整数据库迁移脚本，创建所有表结构 |
| `export_data_backup.py` | 数据备份导出脚本，导出free_templates和resume_thumbs表数据 |
| `import_data_backup.py` | 数据导入脚本，将备份数据导入到生产环境 |
| `deploy_production.py` | 一键部署脚本，自动化执行完整部署流程 |
| `verify_database.py` | 数据库验证脚本，验证迁移结果和数据完整性 |

### 2. 辅助脚本

| 脚本文件 | 功能描述 |
|---------|---------|
| `migrate_error_report_update.py` | 错误上报表字段更新脚本（已有表的升级） |
| `生产环境数据库部署指南.md` | 详细的部署指南文档 |

## 使用流程

### 方式一：一键部署（推荐）

```bash
# 1. 确保所有脚本文件在当前目录
# 2. 执行一键部署
python3 deploy_production.py

# 可选参数：
# --skip-export    跳过数据导出步骤
# --skip-import    跳过数据导入步骤
# --config FILE    使用自定义配置文件
```

### 方式二：分步执行

#### 步骤1：导出开发环境数据
```bash
# 导出free_templates和resume_thumbs表数据
python3 export_data_backup.py
```

#### 步骤2：创建生产环境数据库结构
```bash
# 修改脚本中的数据库配置为生产环境配置
# 然后执行完整数据库迁移
python3 migrate_complete_database.py
```

#### 步骤3：导入数据到生产环境
```bash
# 查看可用的备份文件
python3 import_data_backup.py --list

# 导入free_templates表数据
python3 import_data_backup.py --file data_backups/free_templates_YYYYMMDD_HHMMSS.json --table free_templates --clear

# 导入resume_thumbs表数据
python3 import_data_backup.py --file data_backups/resume_thumbs_YYYYMMDD_HHMMSS.json --table resume_thumbs --clear
```

#### 步骤4：验证迁移结果
```bash
# 验证数据库结构和数据完整性
python3 verify_database.py

# 生产环境验证（修改密码参数）
python3 verify_database.py --password "Resume123!@#Prod"
```

## 配置说明

### 数据库配置

在执行迁移前，需要修改各脚本中的数据库配置：

```python
# 开发环境配置
DB_CONFIG = {
    'host': 'localhost',
    'database': 'resume_service',
    'user': 'resume_user',
    'password': 'Resume123!'
}

# 生产环境配置
DB_CONFIG = {
    'host': 'localhost',
    'database': 'resume_service',
    'user': 'resume_user',
    'password': 'Resume123!@#Prod'  # 生产环境密码
}
```

### 环境变量配置

也可以通过环境变量配置数据库连接：

```bash
export DB_HOST=localhost
export DB_DATABASE=resume_service
export DB_USER=resume_user
export DB_PASSWORD=Resume123!@#Prod
```

## 数据库表结构

迁移脚本会创建以下表：

1. **users** - 用户表
   - 存储微信用户信息
   - 包含会员状态字段

2. **user_actions** - 用户行为记录表
   - 记录用户操作行为
   - 用于数据分析

3. **feedback** - 反馈表
   - 用户反馈信息
   - 支持状态管理

4. **feedback_replies** - 反馈回复表
   - 管理员回复记录

5. **free_templates** - 免费简历模板表
   - 模板信息和下载链接
   - 支持排序和分类

6. **resume_thumbs** - 简历缩略图表
   - 简历样式缩略图
   - 用于前端展示

7. **error_reports** - 错误上报表（增强版）
   - 错误信息收集
   - 包含详细的上下文信息

## 备份和恢复

### 数据备份

```bash
# 导出指定表的数据备份
python3 export_data_backup.py

# 备份文件会保存在 data_backups/ 目录下
# 支持JSON、SQL、mysqldump三种格式
```

### 数据恢复

```bash
# 从JSON文件恢复
python3 import_data_backup.py --file backup.json --table table_name

# 从SQL文件恢复
python3 import_data_backup.py --file backup.sql --format sql

# 从mysqldump文件恢复
python3 import_data_backup.py --file backup_dump.sql --format dump
```

## 验证和测试

### 数据库验证

```bash
# 完整验证
python3 verify_database.py

# 生成验证报告
python3 verify_database.py --output verification_report.json
```

验证内容包括：
- 数据库连接测试
- 表结构完整性检查
- 索引存在性验证
- 数据完整性检查
- 权限验证
- 性能测试

### 应用测试

迁移完成后，建议进行以下测试：

1. **API接口测试**
   ```bash
   # 测试用户认证接口
   curl -X POST http://localhost:18080/auth/login
   
   # 测试模板列表接口
   curl -X GET http://localhost:18080/free-templates
   ```

2. **数据库连接测试**
   ```bash
   # 启动应用并检查日志
   python3 main.py
   ```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否启动
   - 验证用户名和密码
   - 确认数据库权限

2. **表创建失败**
   - 检查用户是否有CREATE权限
   - 验证表名是否冲突
   - 查看MySQL错误日志

3. **数据导入失败**
   - 检查备份文件格式
   - 验证表结构匹配
   - 确认字符集设置

4. **外键约束错误**
   - 检查数据一致性
   - 临时禁用外键检查
   - 按正确顺序导入数据

### 日志文件

- 部署日志：`deploy_logs/deploy_log_YYYYMMDD_HHMMSS.json`
- 验证报告：`verification_report_YYYYMMDD_HHMMSS.json`
- 备份摘要：`data_backups/backup_summary_YYYYMMDD_HHMMSS.json`

## 安全注意事项

1. **密码安全**
   - 生产环境使用强密码
   - 不要在代码中硬编码密码
   - 使用环境变量或配置文件

2. **权限控制**
   - 最小权限原则
   - 定期审查数据库用户权限
   - 限制网络访问

3. **备份安全**
   - 加密敏感备份文件
   - 安全存储备份数据
   - 定期测试恢复流程

## 性能优化

1. **索引优化**
   - 确保关键字段有索引
   - 定期分析查询性能
   - 优化慢查询

2. **连接池配置**
   - 合理设置连接池大小
   - 配置连接超时时间
   - 监控连接使用情况

3. **定期维护**
   - 定期更新表统计信息
   - 清理过期数据
   - 监控磁盘空间使用

## 联系支持

如果在迁移过程中遇到问题，请：

1. 查看相关日志文件
2. 运行验证脚本检查状态
3. 参考故障排除部分
4. 联系技术支持团队

---

**注意：在生产环境执行迁移前，请务必在测试环境中完整验证所有步骤！**
